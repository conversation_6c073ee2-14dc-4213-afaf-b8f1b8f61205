/* Ogólne style */
.pco-product-options {
    margin-bottom: 30px;
}

.pco-product-options h3 {
    margin-bottom: 20px;
    font-size: 1.5em;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

/* Style dla akordeonu */
/* Kontener dla kategorii opcji */
.pco-option-category {
    margin-bottom: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    background-color: #fff;
}

/* Nagłówek akordeonu z efektami hover i aktywnym stanem */
.pco-accordion-header {
    padding: 12px 15px;
    background-color: #f9f9f9;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s;
    border-bottom: 1px solid transparent;
}

.pco-accordion-header:hover {
    background-color: #f0f0f0;
}

.pco-accordion-header.pco-active {
    background-color: #f0f0f0;
    border-bottom: 1px solid #e0e0e0;
}

.pco-accordion-header h4 {
    margin: 0;
    font-size: 1.1em;
    font-weight: 600;
    color: #444;
}

/* Ikona plus/minus dla akordeonu */
.pco-accordion-icon {
    position: relative;
    width: 16px;
    height: 16px;
}

/* Tworzenie ikony plus/minus za pomocą pseudoelementów */
.pco-accordion-icon:before,
.pco-accordion-icon:after {
    content: '';
    position: absolute;
    background-color: #666;
    transition: transform 0.3s ease;
}

/* Pozioma linia ikony */
.pco-accordion-icon:before {
    top: 7px;
    left: 0;
    width: 16px;
    height: 2px;
}

/* Pionowa linia ikony (tworzy plus) */
.pco-accordion-icon:after {
    top: 0;
    left: 7px;
    width: 2px;
    height: 16px;
}

/* Obracanie pionowej linii, aby utworzyć minus gdy akordeon jest otwarty */
.pco-accordion-header.pco-active .pco-accordion-icon:after {
    transform: rotate(90deg);
}

/* Zawartość akordeonu - domyślnie ukryta */
.pco-accordion-content {
    display: none;
    padding: 15px;
    background-color: #fff;
}

/* Klasa dla pokazania zawartości akordeonu */
.pco-accordion-content.pco-show {
    display: block;
}

/* Style dla siatki opcji - responsywna siatka z różną liczbą kolumn */
.pco-option-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr); /* Domyślnie 1 kolumna na małych urządzeniach */
    gap: 10px;
}

/* Media queries dla responsywności siatki */
@media (min-width: 576px) {
    .pco-option-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 kolumny na małych tabletach */
    }
}

@media (min-width: 768px) {
    .pco-option-grid {
        grid-template-columns: repeat(3, 1fr); /* 3 kolumny na dużych tabletach */
    }
}

@media (min-width: 992px) {
    .pco-option-grid {
        grid-template-columns: repeat(4, 1fr); /* 4 kolumny na desktopach */
    }
}

.pco-option-item {
    margin: 0;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    transition: all 0.2s;
}

.pco-option-item:hover {
    background-color: #f0f0f0;
    border-color: #d5d5d5;
}

.pco-option-item label {
    display: block;
    width: 100%;
    cursor: pointer;
}

.pco-option-price {
    color: #888;
    font-size: 0.9em;
    font-weight: normal;
}

/* Style dla selectów */
.pco-option-select {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    font-size: 1em;
}

/* Style dla podsumowania ceny */
.pco-price-summary {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    margin-top: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.pco-price-summary h4 {
    margin-bottom: 15px;
    font-size: 1.3em;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    color: #333;
}

.pco-base-price-wrapper,
.pco-options-price-wrapper,
.pco-total-price-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
}

.pco-price-label {
    font-weight: 500;
    color: #555;
}

.pco-total-price-wrapper {
    margin-top: 10px;
    padding-top: 10px;
    font-weight: bold;
    border-top: 1px dashed #ddd;
    border-bottom: 1px dashed #ddd;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

/* Style dla wybranych opcji */
.pco-selected-options {
    margin: 15px 0;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border-left: 3px solid #ddd;
}

.pco-selected-options h5 {
    margin-bottom: 10px;
    font-size: 1.1em;
    color: #555;
}

.pco-selected-option-category {
    margin-bottom: 8px;
    padding-bottom: 5px;
    line-height: 1.4;
}

.pco-selected-option-category:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.pco-selected-option-category-name {
    font-weight: bold;
    color: #555;
}

/* Style dla podsumowania */
.pco-summary-section {
    background-color: #f0f0f0;
    padding: 15px;
    border-radius: 4px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

.pco-summary-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    padding: 5px 0;
}

.pco-summary-total {
    font-weight: bold;
    font-size: 1.1em;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed #ddd;
}

/* Style dla formularza zamówienia */
.pco-order-form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.pco-order-form-wrapper {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.pco-order-form-wrapper h1 {
    margin-bottom: 30px;
    text-align: center;
}

.pco-order-summary {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 30px;
}

.pco-order-summary h2 {
    margin-bottom: 20px;
    font-size: 1.5em;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.pco-summary-product {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.pco-summary-product h3 {
    margin: 0 0 10px 0;
}

.pco-summary-image {
    margin-right: 20px;
}

.pco-summary-details {
    flex: 1;
}

.pco-summary-options ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.pco-summary-price {
    margin-top: 15px;
    font-weight: bold;
}

.pco-form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.pco-form-section h2 {
    margin-bottom: 20px;
    font-size: 1.3em;
}

.pco-form-field {
    margin-bottom: 15px;
}

.pco-form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.pco-form-field input[type="text"],
.pco-form-field input[type="email"],
.pco-form-field input[type="tel"],
.pco-form-field input[type="date"],
.pco-form-field input[type="time"],
.pco-form-field textarea,
.pco-form-field select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.pco-form-actions {
    text-align: center;
    margin-top: 30px;
}

.pco-submit-button {
    background-color: transparent;
    color: var(--color-accent);
    border: 1px solid var(--color-accent);
    border-radius: 0;
    padding: var(--spacing-s) var(--spacing-xl);
    font-family: var(--font-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-medium);
    align-self: flex-start;
    margin-top: var(--spacing-m);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.pco-submit-button:hover {
    background-color: var(--color-accent);
    color: var(--color-white);
}

/* Style dla strony potwierdzenia */
.pco-confirmation-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
}

.pco-confirmation-wrapper {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.pco-confirmation-wrapper h1 {
    margin-bottom: 30px;
    color: var(--color-deep-gray);
}

.pco-confirmation-message {
    margin-bottom: 30px;
    font-size: 1.1em;
    line-height: 1.6;
}

.pco-confirmation-actions {
    margin-top: 30px;
}

.pco-button {
    display: inline-block;
    background-color: var(--color-accent);
    color: #fff;
    padding: 12px 30px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
}

.pco-button:hover {
    background-color: var(--color-accent-hover);
    color: #fff;
}