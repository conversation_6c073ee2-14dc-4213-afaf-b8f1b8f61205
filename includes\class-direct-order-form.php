<?php
/**
 * Klasa obsługująca niestandardowy formularz zamówienia
 */
class PCO_Direct_Order_Form {

    /**
     * Konstruktor
     */
    public function __construct() {
        // Modyfikacja przycisku dodawania do koszyka
        add_filter('woocommerce_product_single_add_to_cart_text', array($this, 'modify_add_to_cart_text'), 10, 2);

        // Przechwycenie akcji dodawania do koszyka
        add_filter('woocommerce_add_to_cart_validation', array($this, 'intercept_add_to_cart'), 10, 5);

        // Dodanie nagłówków UTF-8 do maili
        add_filter('wp_mail_content_type', array($this, 'set_mail_content_type'));

        // Obsługa przesyłania formularza
        add_action('wp_ajax_pco_submit_form', array($this, 'process_form_submission'));
        add_action('wp_ajax_nopriv_pco_submit_form', array($this, 'process_form_submission'));

        // Dodanie danych zamówienia do formularza
        add_action('pco_before_order_form', array($this, 'add_order_summary'));
    }

    /**
     * Modyfikacja tekstu przycisku "Dodaj do koszyka"
     */
    public function modify_add_to_cart_text($text, $product) {
        // Sprawdzenie czy niestandardowy formularz jest włączony dla tego produktu
        $enable_custom_form = get_post_meta($product->get_id(), '_pco_enable_custom_form', true);
        if ($enable_custom_form === 'yes') {
            return __('Złóż zamówienie', 'papierotka-custom-order');
        }

        return $text;
    }

    /**
     * Przechwycenie akcji dodawania do koszyka
     */
    public function intercept_add_to_cart($valid, $product_id, $quantity, $variation_id = 0, $variations = array()) {
        // Sprawdzenie czy to jest niestandardowe zamówienie
        if (isset($_POST['pco_redirect_to_form']) && $_POST['pco_redirect_to_form'] == 1) {
            // Sprawdzenie czy niestandardowy formularz jest włączony dla tego produktu
            $enable_custom_form = get_post_meta($product_id, '_pco_enable_custom_form', true);
            if ($enable_custom_form === 'yes') {
                // Debugowanie - wyświetlenie podstawowych informacji
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    pco_log('Przechwycenie zamówienia - przed zapisem sesji');
                    pco_log('User logged in: ' . (is_user_logged_in() ? 'Yes' : 'No'));
                    pco_log('Session exists: ' . (WC()->session ? 'Yes' : 'No'));
                }

                // Upewnienie się, że sesja WooCommerce jest inicjalizowana
                if (!WC()->session->has_session()) {
                    WC()->session->set_customer_session_cookie(true);
                }

                // Zapisanie danych w sesji
                WC()->session->set('pco_product_id', $product_id);
                WC()->session->set('pco_quantity', $quantity);

                // Debugowanie - wyświetlenie podstawowych informacji
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    pco_log('Dane zapisane w sesji: product_id=' . $product_id . ', quantity=' . $quantity);
                }

                // Poprawna obsługa kodowania UTF-8 dla danych opcji
                $options_data = array();
                if (isset($_POST['pco_options_data'])) {
                    $json_data = stripslashes($_POST['pco_options_data']);
                    $options_data = json_decode($json_data, true);

                    // Debugowanie
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        pco_log('Dane opcji przed zapisem w sesji:');
                        pco_log(print_r($options_data, true));
                    }
                }

                WC()->session->set('pco_options_data', $options_data);
                WC()->session->set('pco_total_price', isset($_POST['pco_total_price']) ? floatval($_POST['pco_total_price']) : 0);

                // Przekierowanie do formularza zamówienia
                $redirect_url = site_url('/zamowienie/');
                wp_redirect($redirect_url);
                exit;
            }
        }

        return $valid;
    }

    /**
     * Dodanie podsumowania zamówienia do formularza
     */
    public function add_order_summary() {
        // Pobranie danych z sesji
        $product_id = WC()->session->get('pco_product_id');
        $quantity = WC()->session->get('pco_quantity');
        $options_data = WC()->session->get('pco_options_data');
        $total_price = WC()->session->get('pco_total_price');

        // Debugowanie - wyświetlenie struktury danych opcji
        if (defined('WP_DEBUG') && WP_DEBUG) {
            pco_log('Struktura danych opcji w formularzu zamówienia:');
            pco_log(print_r($options_data, true));
        }

        if (!$product_id) {
            return;
        }

        $product = wc_get_product($product_id);
        if (!$product) {
            return;
        }

        // Wyświetlenie podsumowania zamówienia
        ?>
        <div class="pco-order-summary">
            <h2><?php _e('Podsumowanie zamówienia', 'papierotka-custom-order'); ?></h2>

            <div class="pco-summary-product">
                <h3><?php echo esc_html($product->get_name()); ?></h3>
                <p class="pco-summary-image"><?php echo $product->get_image('thumbnail'); ?></p>
            </div>

            <div class="pco-summary-details">
                <p><strong><?php _e('Ilość:', 'papierotka-custom-order'); ?></strong> <?php echo esc_html($quantity); ?></p>

                <?php if (!empty($options_data)) : ?>
                <div class="pco-summary-options">
                    <p><strong><?php _e('Wybrane opcje:', 'papierotka-custom-order'); ?></strong></p>
                    <ul>
                        <?php foreach ($options_data as $category => $options) : ?>
                            <li>
                                <strong><?php echo esc_html($this->get_category_name($category)); ?>:</strong>
                                <?php
                                if (is_array($options)) {
                                    // Dla multiselect
                                    $option_names = array_map(function($option) {
                                        return $option['name'];
                                    }, $options);
                                    echo esc_html(implode(', ', $option_names));
                                } else {
                                    // Dla pojedynczego wyboru - teraz options to bezpośrednio ciąg znaków
                                    echo esc_html($options);
                                }
                                ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <div class="pco-summary-price">
                    <p><strong><?php _e('Cena za 1 zaproszenie:', 'papierotka-custom-order'); ?></strong> <?php echo wc_price($total_price); ?></p>
                    <p><strong><?php _e('Cena łączna:', 'papierotka-custom-order'); ?></strong> <?php echo wc_price($total_price * $quantity); ?></p>
                </div>
            </div>

            <input type="hidden" name="pco_product_id" value="<?php echo esc_attr($product_id); ?>">
            <input type="hidden" name="pco_quantity" value="<?php echo esc_attr($quantity); ?>">
            <input type="hidden" name="pco_options_data" value='<?php echo esc_attr(json_encode($options_data, JSON_UNESCAPED_UNICODE)); ?>'>
            <input type="hidden" name="pco_total_price" value="<?php echo esc_attr($total_price); ?>">
        </div>
        <?php
    }

    /**
     * Obsługa przesyłania formularza
     */
    public function process_form_submission() {
        // Debug: Log form submission attempt
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('PCO: Form submission started');
            error_log('PCO: POST data: ' . print_r($_POST, true));
            error_log('PCO: FILES data: ' . print_r($_FILES, true));
        }

        // Sprawdzenie czy dane POST istnieją
        if (empty($_POST)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Empty POST data');
            }
            wp_send_json_error(array('message' => __('Brak danych formularza.', 'papierotka-custom-order')));
            return;
        }

        // Sprawdzenie czy nonce istnieje
        if (!isset($_POST['nonce'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Nonce not provided');
            }
            wp_send_json_error(array('message' => __('Błąd bezpieczeństwa: brak tokenu.', 'papierotka-custom-order')));
            return;
        }

        // Sprawdzenie nonce - zwracamy JSON error zamiast wp_die
        if (!wp_verify_nonce($_POST['nonce'], 'pco-ajax-nonce')) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Nonce verification failed. Expected: pco-ajax-nonce, Received: ' . $_POST['nonce']);
            }
            wp_send_json_error(array('message' => __('Błąd bezpieczeństwa. Odśwież stronę i spróbuj ponownie.', 'papierotka-custom-order')));
            return;
        }

        // Pobranie danych formularza
        if (!isset($_POST['form_data'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: form_data not provided in POST');
            }
            wp_send_json_error(array('message' => __('Brak danych formularza w żądaniu.', 'papierotka-custom-order')));
            return;
        }

        $form_data_json = stripslashes($_POST['form_data']);
        $form_data = json_decode($form_data_json, true);

        // Sprawdzenie czy JSON został poprawnie zdekodowany
        if (json_last_error() !== JSON_ERROR_NONE) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: JSON decode error: ' . json_last_error_msg());
                error_log('PCO: Raw form_data: ' . $form_data_json);
            }
            wp_send_json_error(array('message' => __('Błąd dekodowania danych formularza.', 'papierotka-custom-order')));
            return;
        }

        // Debugowanie - wyświetlenie otrzymanych danych
        if (defined('WP_DEBUG') && WP_DEBUG) {
            pco_log('Otrzymane dane formularza:');
            pco_log(print_r($form_data, true));
        }

        if (empty($form_data)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: form_data is empty after decoding');
            }
            wp_send_json_error(array('message' => __('Dane formularza są puste.', 'papierotka-custom-order')));
            return;
        }

        // Sprawdzenie czy dane produktu są dostępne
        if (!isset($form_data['pco_product_id']) || intval($form_data['pco_product_id']) <= 0) {
            wp_send_json_error(array('message' => __('Brak identyfikatora produktu', 'papierotka-custom-order')));
            return;
        }

        // Walidacja formularza za pomocą form buildera
        try {
            $form_builder = new PCO_Form_Builder();
            $validation_errors = $form_builder->validate_form_data($form_data);

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Validation completed. Errors: ' . print_r($validation_errors, true));
            }
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Validation error: ' . $e->getMessage());
            }
            wp_send_json_error(array('message' => __('Błąd podczas walidacji formularza.', 'papierotka-custom-order')));
            return;
        }

        // Dodatkowa walidacja specyficzna dla biznesu
        if (!empty($form_data['wedding_date'])) {
            $wedding_date = DateTime::createFromFormat('Y-m-d', $form_data['wedding_date']);
            if ($wedding_date && $wedding_date < new DateTime()) {
                $validation_errors[] = __('Data ślubu nie może być z przeszłości.', 'papierotka-custom-order');
            }
        }

        if (!empty($validation_errors)) {
            wp_send_json_error(array('message' => implode('<br>', $validation_errors)));
            return;
        }

        // Przetwarzanie danych opcji - upewnienie się, że polskie znaki są poprawnie obsługiwane
        if (isset($form_data['pco_options_data']) && !is_array($form_data['pco_options_data'])) {
            $form_data['pco_options_data'] = json_decode($form_data['pco_options_data'], true);

            // Debugowanie - wyświetlenie zdekodowanych danych opcji
            if (defined('WP_DEBUG') && WP_DEBUG) {
                pco_log('Zdekodowane dane opcji:');
                pco_log(print_r($form_data['pco_options_data'], true));
            }
        }

        // Pobranie danych produktu
        $product_id = isset($form_data['pco_product_id']) ? intval($form_data['pco_product_id']) : 0;
        $quantity = isset($form_data['pco_quantity']) ? intval($form_data['pco_quantity']) : 1;
        $options_data = isset($form_data['pco_options_data']) ? $form_data['pco_options_data'] : array();
        $total_price = isset($form_data['pco_total_price']) ? floatval($form_data['pco_total_price']) : 0;

        if (!$product_id) {
            wp_send_json_error(array('message' => __('Brak identyfikatora produktu', 'papierotka-custom-order')));
            return;
        }

        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(array('message' => __('Produkt nie istnieje', 'papierotka-custom-order')));
            return;
        }

        // Generowanie unikalnego ID zamówienia
        $order_id = 'PCO-' . date('Ymd') . '-' . substr(uniqid(), -5);

        // Zapisanie zamówienia w bazie danych
        try {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Attempting to save order to database...');
            }

            $saved = $this->save_order_to_database($order_id, $form_data);

            if (!$saved) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('PCO: save_order_to_database returned false');
                }
                wp_send_json_error(array('message' => __('Błąd podczas zapisywania zamówienia.', 'papierotka-custom-order')));
                return;
            }

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Order saved to database with ID: ' . $order_id . ', insert_id: ' . $saved);
            }
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Database exception: ' . $e->getMessage());
                error_log('PCO: Exception trace: ' . $e->getTraceAsString());
            }
            wp_send_json_error(array('message' => __('Błąd podczas zapisywania zamówienia: ', 'papierotka-custom-order') . $e->getMessage()));
            return;
        }

        // Wysłanie powiadomienia e-mail
        try {
            $email_sent = $this->send_order_notification($order_id, $form_data);
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Email notification sent: ' . ($email_sent ? 'yes' : 'no'));
            }
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Email error: ' . $e->getMessage());
            }
            // Don't fail the order if email fails, just log it
        }

        // Wyczyszczenie sesji
        WC()->session->set('pco_product_id', null);
        WC()->session->set('pco_quantity', null);
        WC()->session->set('pco_options_data', null);
        WC()->session->set('pco_total_price', null);

        // Przygotowanie URL przekierowania
        $redirect_url = add_query_arg('order_id', $order_id, site_url('/potwierdzenie-zamowienia/'));

        // Zwrócenie odpowiedzi
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('PCO: Form submission completed successfully. Order ID: ' . $order_id);
            error_log('PCO: Redirect URL: ' . $redirect_url);
        }

        wp_send_json_success(array(
            'message' => __('Zamówienie zostało przyjęte', 'papierotka-custom-order'),
            'order_id' => $order_id,
            'redirect' => $redirect_url
        ));
    }

    /**
     * Zapisanie zamówienia w bazie danych
     */
    private function save_order_to_database($order_id, $form_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pco_orders';

        // Sprawdzenie czy tabela istnieje, jeśli nie - utworzenie jej
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Table does not exist, creating: ' . $table_name);
            }
            $this->create_orders_table();
        } else {
            // Sprawdzenie czy tabela ma prawidłową strukturę
            $this->ensure_table_structure($table_name);
        }

        // Przygotowanie danych do zapisu z bezpiecznym kodowaniem
        $product_id = isset($form_data['pco_product_id']) ? intval($form_data['pco_product_id']) : 0;
        $quantity = isset($form_data['pco_quantity']) ? intval($form_data['pco_quantity']) : 1;
        $total_price = isset($form_data['pco_total_price']) ? floatval($form_data['pco_total_price']) : 0;

        // Bezpieczne przetwarzanie danych opcji
        $options_data = '';
        if (isset($form_data['pco_options_data'])) {
            if (is_array($form_data['pco_options_data'])) {
                $options_data = json_encode($form_data['pco_options_data'], JSON_UNESCAPED_UNICODE);
            } else if (is_string($form_data['pco_options_data'])) {
                // Sprawdzenie czy to już jest JSON
                $decoded = json_decode($form_data['pco_options_data'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $options_data = $form_data['pco_options_data'];
                } else {
                    $options_data = json_encode($form_data['pco_options_data'], JSON_UNESCAPED_UNICODE);
                }
            }
        }

        // Bezpieczne przetwarzanie wszystkich danych formularza
        $form_data_json = json_encode($form_data, JSON_UNESCAPED_UNICODE);

        // Debugowanie przed zapisem
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('PCO: Preparing to save order:');
            error_log('PCO: Order ID: ' . $order_id);
            error_log('PCO: Product ID: ' . $product_id);
            error_log('PCO: Quantity: ' . $quantity);
            error_log('PCO: Total Price: ' . $total_price);
            error_log('PCO: Options Data Length: ' . strlen($options_data));
            error_log('PCO: Form Data Length: ' . strlen($form_data_json));
        }

        // Sprawdzenie czy JSON encoding się powiódł
        if ($form_data_json === false) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: JSON encoding failed for form_data: ' . json_last_error_msg());
            }
            return false;
        }

        // Zapisanie zamówienia
        $data_to_insert = array(
            'order_id' => $order_id,
            'product_id' => $product_id,
            'quantity' => $quantity,
            'options_data' => $options_data,
            'total_price' => $total_price,
            'form_data' => $form_data_json,
            'created_at' => current_time('mysql')
        );

        $result = $wpdb->insert($table_name, $data_to_insert);

        if ($result === false) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Database insert failed. Error: ' . $wpdb->last_error);
                error_log('PCO: Last query: ' . $wpdb->last_query);
                error_log('PCO: Data to insert: ' . print_r($data_to_insert, true));
            }
            return false;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('PCO: Order saved successfully with insert ID: ' . $wpdb->insert_id);
        }

        return $wpdb->insert_id;
    }

    /**
     * Utworzenie tabeli zamówień
     */
    private function create_orders_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pco_orders';
        $charset_collate = $wpdb->get_charset_collate();

        // Upewnienie się, że używamy UTF-8
        if (empty($charset_collate)) {
            $charset_collate = 'DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci';
        }

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            order_id varchar(20) NOT NULL,
            product_id bigint(20) NOT NULL,
            quantity int(11) NOT NULL,
            options_data longtext NOT NULL,
            total_price decimal(10,2) NOT NULL,
            form_data longtext NOT NULL,
            created_at datetime NOT NULL,
            PRIMARY KEY  (id),
            UNIQUE KEY order_id (order_id)
        ) $charset_collate;";

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('PCO: Creating table with SQL: ' . $sql);
        }

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $result = dbDelta($sql);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('PCO: dbDelta result: ' . print_r($result, true));

            // Sprawdzenie czy tabela została utworzona
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
            error_log('PCO: Table exists after creation: ' . ($table_exists ? 'yes' : 'no'));
        }
    }

    /**
     * Sprawdzenie i naprawa struktury tabeli
     */
    private function ensure_table_structure($table_name) {
        global $wpdb;

        // Sprawdzenie obecnej struktury
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        $existing_columns = array();
        foreach ($columns as $column) {
            $existing_columns[] = $column->Field;
        }

        $required_columns = array('product_id', 'quantity', 'options_data', 'total_price', 'form_data', 'created_at');
        $missing_columns = array_diff($required_columns, $existing_columns);

        if (!empty($missing_columns)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO: Table structure is outdated, missing columns: ' . implode(', ', $missing_columns));
                error_log('PCO: Recreating table with correct structure');
            }

            // Jeśli brakuje kluczowych kolumn, odtwórz tabelę
            $wpdb->query("DROP TABLE IF EXISTS $table_name");
            $this->create_orders_table();
        }
    }

    /**
     * Wysłanie powiadomienia e-mail
     */
    private function send_order_notification($order_id, $form_data) {
        $admin_email = get_option('admin_email');
        $customer_email = isset($form_data['email']) ? sanitize_email($form_data['email']) : '';

        if (empty($customer_email)) {
            return false;
        }

        // Inicjalizacja klasy szablonów e-mail
        $email_templates = new PCO_Email_Templates();

        // Generowanie zmiennych dla szablonów
        $template_variables = $email_templates->generate_template_variables($order_id, $form_data);

        // Wysłanie e-maila do administratora
        $this->send_admin_notification($admin_email, $email_templates, $template_variables);

        // Wysłanie potwierdzenia do klienta
        $this->send_customer_confirmation($customer_email, $email_templates, $template_variables);

        return true;
    }

    /**
     * Wysłanie powiadomienia do administratora
     */
    private function send_admin_notification($admin_email, $email_templates, $template_variables) {
        // Pobranie szablonu dla administratora
        $admin_template = $email_templates->get_template('admin_notification');

        if (!$admin_template || !$admin_template['enabled']) {
            return false;
        }

        // Przetworzenie szablonu
        $subject = $email_templates->process_template($admin_template['subject'], $template_variables);
        $message = $email_templates->process_template($admin_template['content'], $template_variables);

        // Wysłanie e-maila
        return wp_mail($admin_email, $subject, $message);
    }

    /**
     * Wysłanie potwierdzenia do klienta
     */
    private function send_customer_confirmation($customer_email, $email_templates, $template_variables) {
        // Pobranie szablonu dla klienta
        $customer_template = $email_templates->get_template('customer_confirmation');

        if (!$customer_template || !$customer_template['enabled']) {
            return false;
        }

        // Przetworzenie szablonu
        $subject = $email_templates->process_template($customer_template['subject'], $template_variables);
        $message = $email_templates->process_template($customer_template['content'], $template_variables);

        // Wysłanie e-maila
        return wp_mail($customer_email, $subject, $message);
    }

    /**
     * Stara metoda wysyłania powiadomień (zachowana dla kompatybilności wstecznej)
     * @deprecated Użyj send_order_notification() zamiast tej metody
     */
    private function send_order_notification_legacy($order_id, $form_data) {
        $admin_email = get_option('admin_email');
        $customer_email = isset($form_data['email']) ? sanitize_email($form_data['email']) : '';

        if (empty($customer_email)) {
            return false;
        }

        // Pobranie danych produktu
        $product_id = isset($form_data['pco_product_id']) ? intval($form_data['pco_product_id']) : 0;
        $product = wc_get_product($product_id);
        $product_name = $product ? $product->get_name() : __('Produkt', 'papierotka-custom-order');

        // Przygotowanie treści wiadomości
        $subject = sprintf(__('Nowe zamówienie #%s', 'papierotka-custom-order'), $order_id);

        // Ta metoda została zastąpiona przez nowy system szablonów e-mail
        // Kod został przeniesiony do klasy PCO_Email_Templates
        // Aby przywrócić stary sposób wysyłania e-maili, odkomentuj poniższy kod

        /*
        // Początek HTML
        $message = '<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /></head><body>';
        // ... reszta starego kodu ...
        */

        // Przekierowanie do nowej metody
        return $this->send_order_notification($order_id, $form_data);
    }

    /**
     * Pobieranie nazwy kategorii
     */
    private function get_category_name($category_id) {
        $categories = array(
            'koperta' => __('Koperta', 'papierotka-custom-order'),
            'personalizacja_koperty' => __('Personalizacja koperty', 'papierotka-custom-order'),
            'lak' => __('Lak', 'papierotka-custom-order'),
            'karty_zaproszenia' => __('Karty zaproszenia', 'papierotka-custom-order'),
            'zlocenia' => __('Złocenia', 'papierotka-custom-order'),
            'wstazka_sznurek' => __('Wstążka / Sznurek', 'papierotka-custom-order'),
            'opaska' => __('Opaska', 'papierotka-custom-order'),
            'dodatki' => __('Dodatki', 'papierotka-custom-order')
        );

        return isset($categories[$category_id]) ? $categories[$category_id] : $category_id;
    }

    /**
     * Ustawienie typu zawartości maila jako HTML z kodowaniem UTF-8
     */
    public function set_mail_content_type() {
        return 'text/html; charset=UTF-8';
    }
}