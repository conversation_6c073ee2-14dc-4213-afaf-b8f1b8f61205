<?php
/**
 * <PERSON>lasa zarządzająca ustawieniami e-mail w panelu administracyjnym
 */
class PCO_Email_Settings {

    private $email_templates;

    /**
     * Konstruktor
     */
    public function __construct() {
        $this->email_templates = new PCO_Email_Templates();

        // Dodanie menu w panelu administracyjnym
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Obsługa zapisywania ustawień
        add_action('admin_post_pco_save_email_settings', array($this, 'save_email_settings'));

        // Dodanie skryptów i stylów dla panelu administracyjnego
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Obsługa AJAX dla podglądu e-maili
        add_action('wp_ajax_pco_preview_email', array($this, 'ajax_preview_email'));
    }

    /**
     * Dodanie menu w panelu administracyjnym
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Powiadomienia E-mail - Papierotka', 'papierotka-custom-order'),
            __('Powiadomienia E-mail', 'papierotka-custom-order'),
            'manage_woocommerce',
            'pco-email-settings',
            array($this, 'admin_page')
        );
    }

    /**
     * Strona ustawień w panelu administracyjnym
     */
    public function admin_page() {
        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nie masz uprawnień do tej strony.', 'papierotka-custom-order'));
        }

        // Pobranie aktualnych szablonów
        $admin_template = $this->email_templates->get_template('admin_notification');
        $customer_template = $this->email_templates->get_template('customer_confirmation');

        // Pobranie dostępnych zmiennych
        $available_variables = $this->email_templates->get_available_variables();

        ?>
        <div class="wrap">
            <h1><?php _e('Ustawienia Powiadomień E-mail', 'papierotka-custom-order'); ?></h1>

            <?php
            // Wyświetlenie komunikatu o zapisaniu
            if (isset($_GET['settings-updated']) && $_GET['settings-updated'] == 'true') {
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Ustawienia zostały zapisane.', 'papierotka-custom-order') . '</p></div>';
            }
            ?>

            <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
                <?php wp_nonce_field('pco_email_settings', 'pco_email_nonce'); ?>
                <input type="hidden" name="action" value="pco_save_email_settings">

                <div class="pco-email-settings">
                    <!-- Dostępne zmienne -->
                    <div class="pco-variables-info">
                        <h3><?php _e('Dostępne zmienne', 'papierotka-custom-order'); ?></h3>
                        <p><?php _e('Możesz używać następujących zmiennych w szablonach e-mail:', 'papierotka-custom-order'); ?></p>
                        <div class="pco-variables-list">
                            <?php foreach ($available_variables as $variable => $description): ?>
                                <span class="pco-variable" onclick="insertVariable('<?php echo esc_attr($variable); ?>')">
                                    <code><?php echo esc_html($variable); ?></code> - <?php echo esc_html($description); ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Szablon dla administratora -->
                    <div class="pco-template-section">
                        <h2><?php _e('Powiadomienie dla Administratora', 'papierotka-custom-order'); ?></h2>

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="admin_enabled"><?php _e('Włączone', 'papierotka-custom-order'); ?></label>
                                </th>
                                <td>
                                    <input type="checkbox" id="admin_enabled" name="admin_enabled" value="1"
                                           <?php checked($admin_template['enabled'], true); ?>>
                                    <p class="description"><?php _e('Zaznacz, aby włączyć wysyłanie powiadomień do administratora.', 'papierotka-custom-order'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="admin_subject"><?php _e('Temat wiadomości', 'papierotka-custom-order'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="admin_subject" name="admin_subject"
                                           value="<?php echo esc_attr($admin_template['subject']); ?>"
                                           class="regular-text" required>
                                    <p class="description"><?php _e('Temat wiadomości e-mail wysyłanej do administratora.', 'papierotka-custom-order'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="admin_content"><?php _e('Treść wiadomości', 'papierotka-custom-order'); ?></label>
                                </th>
                                <td>
                                    <?php
                                    wp_editor(
                                        $admin_template['content'],
                                        'admin_content',
                                        array(
                                            'textarea_name' => 'admin_content',
                                            'textarea_rows' => 15,
                                            'media_buttons' => false,
                                            'teeny' => false,
                                            'tinymce' => array(
                                                'toolbar1' => 'bold,italic,underline,strikethrough,|,bullist,numlist,|,link,unlink,|,undo,redo',
                                                'toolbar2' => 'formatselect,|,forecolor,backcolor,|,alignleft,aligncenter,alignright,alignjustify'
                                            )
                                        )
                                    );
                                    ?>
                                    <p class="description"><?php _e('Treść wiadomości e-mail wysyłanej do administratora. Możesz używać HTML i zmiennych.', 'papierotka-custom-order'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Szablon dla klienta -->
                    <div class="pco-template-section">
                        <h2><?php _e('Potwierdzenie dla Klienta', 'papierotka-custom-order'); ?></h2>

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="customer_enabled"><?php _e('Włączone', 'papierotka-custom-order'); ?></label>
                                </th>
                                <td>
                                    <input type="checkbox" id="customer_enabled" name="customer_enabled" value="1"
                                           <?php checked($customer_template['enabled'], true); ?>>
                                    <p class="description"><?php _e('Zaznacz, aby włączyć wysyłanie potwierdzeń do klientów.', 'papierotka-custom-order'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="customer_subject"><?php _e('Temat wiadomości', 'papierotka-custom-order'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="customer_subject" name="customer_subject"
                                           value="<?php echo esc_attr($customer_template['subject']); ?>"
                                           class="regular-text" required>
                                    <p class="description"><?php _e('Temat wiadomości e-mail wysyłanej do klienta.', 'papierotka-custom-order'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="customer_content"><?php _e('Treść wiadomości', 'papierotka-custom-order'); ?></label>
                                </th>
                                <td>
                                    <?php
                                    wp_editor(
                                        $customer_template['content'],
                                        'customer_content',
                                        array(
                                            'textarea_name' => 'customer_content',
                                            'textarea_rows' => 15,
                                            'media_buttons' => false,
                                            'teeny' => false,
                                            'tinymce' => array(
                                                'toolbar1' => 'bold,italic,underline,strikethrough,|,bullist,numlist,|,link,unlink,|,undo,redo',
                                                'toolbar2' => 'formatselect,|,forecolor,backcolor,|,alignleft,aligncenter,alignright,alignjustify'
                                            )
                                        )
                                    );
                                    ?>
                                    <p class="description"><?php _e('Treść wiadomości e-mail wysyłanej do klienta. Możesz używać HTML i zmiennych.', 'papierotka-custom-order'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Przyciski akcji -->
                    <div class="pco-actions">
                        <?php submit_button(__('Zapisz ustawienia', 'papierotka-custom-order'), 'primary', 'submit', false); ?>
                        <button type="button" class="button" onclick="previewEmail('admin')"><?php _e('Podgląd e-maila dla administratora', 'papierotka-custom-order'); ?></button>
                        <button type="button" class="button" onclick="previewEmail('customer')"><?php _e('Podgląd e-maila dla klienta', 'papierotka-custom-order'); ?></button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal do podglądu e-maila -->
        <div id="pco-email-preview-modal" style="display: none;">
            <div class="pco-modal-content">
                <span class="pco-modal-close">&times;</span>
                <h2><?php _e('Podgląd e-maila', 'papierotka-custom-order'); ?></h2>
                <div id="pco-email-preview-content"></div>
            </div>
        </div>
        <?php
    }

    /**
     * Zapisanie ustawień e-mail
     */
    public function save_email_settings() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['pco_email_nonce'], 'pco_email_settings')) {
            wp_die(__('Błąd bezpieczeństwa.', 'papierotka-custom-order'));
        }

        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nie masz uprawnień do tej akcji.', 'papierotka-custom-order'));
        }

        // Zapisanie szablonu dla administratora
        $admin_enabled = isset($_POST['admin_enabled']) ? true : false;
        $admin_subject = sanitize_text_field($_POST['admin_subject']);
        $admin_content = wp_kses_post($_POST['admin_content']);

        $this->email_templates->save_template('admin_notification', $admin_subject, $admin_content, $admin_enabled);

        // Zapisanie szablonu dla klienta
        $customer_enabled = isset($_POST['customer_enabled']) ? true : false;
        $customer_subject = sanitize_text_field($_POST['customer_subject']);
        $customer_content = wp_kses_post($_POST['customer_content']);

        $this->email_templates->save_template('customer_confirmation', $customer_subject, $customer_content, $customer_enabled);

        // Przekierowanie z komunikatem o sukcesie
        wp_redirect(admin_url('admin.php?page=pco-email-settings&settings-updated=true'));
        exit;
    }

    /**
     * Dodanie skryptów i stylów dla panelu administracyjnego
     */
    public function enqueue_admin_scripts($hook) {
        // Ładowanie tylko na stronie ustawień e-mail
        if ($hook !== 'woocommerce_page_pco-email-settings') {
            return;
        }

        wp_enqueue_style('pco-admin-email-settings', PCO_PLUGIN_URL . 'assets/css/admin-email-settings.css', array(), PCO_VERSION);
        wp_enqueue_script('pco-admin-email-settings', PCO_PLUGIN_URL . 'assets/js/admin-email-settings.js', array('jquery'), PCO_VERSION, true);

        // Przekazanie danych do skryptu
        wp_localize_script('pco-admin-email-settings', 'pco_admin_data', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('pco-admin-nonce'),
            'preview_url' => admin_url('admin-ajax.php?action=pco_preview_email')
        ));
    }

    /**
     * AJAX handler dla podglądu e-maili
     */
    public function ajax_preview_email() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pco-admin-nonce')) {
            wp_die('Błąd bezpieczeństwa');
        }

        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Brak uprawnień');
        }

        $template_type = sanitize_text_field($_POST['template_type']);
        $subject = sanitize_text_field($_POST['subject']);
        $content = wp_kses_post($_POST['content']);

        // Przykładowe dane do podglądu
        $sample_variables = array(
            'order_id' => 'PCO-20250101-12345',
            'customer_name' => 'Jan Kowalski',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+48 123 456 789',
            'bride_name' => 'Anna',
            'groom_name' => 'Jan',
            'wedding_date' => '2025-06-15',
            'wedding_time' => '15:00',
            'product_name' => 'Eleganckie zaproszenia ślubne',
            'quantity' => '50',
            'total_price' => '750.00',
            'order_details' => '<h3>Szczegółowe dane zamówienia:</h3><ul><li><strong>Nazwa kościoła:</strong> Kościół św. Jana</li><li><strong>Adres kościoła:</strong> ul. Kościelna 1, Warszawa</li></ul>',
            'site_name' => get_bloginfo('name'),
            'site_url' => home_url()
        );

        // Przetworzenie szablonu
        $processed_subject = $this->email_templates->process_template($subject, $sample_variables);
        $processed_content = $this->email_templates->process_template($content, $sample_variables);

        // Zwrócenie przetworzonego szablonu
        wp_send_json_success(array(
            'subject' => $processed_subject,
            'content' => $processed_content
        ));
    }
}
