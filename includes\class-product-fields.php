<?php
/**
 * Klasa zarządzająca polami produktu
 */
class PCO_Product_Fields {
    
    /**
     * Konstruktor
     */
    public function __construct() {
        // Dodanie pól do produktu
        add_action('woocommerce_product_options_general_product_data', array($this, 'add_product_options'));
        add_action('woocommerce_process_product_meta', array($this, 'save_product_options'));
        
        // Dodanie pól do strony produktu
        add_action('woocommerce_before_add_to_cart_button', array($this, 'display_product_options'));
        
        // Obsługa AJAX dla aktualizacji ceny
        add_action('wp_ajax_pco_update_price', array($this, 'ajax_update_price'));
        add_action('wp_ajax_nopriv_pco_update_price', array($this, 'ajax_update_price'));
    }
    
    /**
     * Dodanie opcji do panelu administracyjnego produktu
     */
    public function add_product_options() {
        global $woocommerce, $post;
        
        echo '<div class="options_group">';
        
        // Pole do włączenia niestandardowego formularza
        woocommerce_wp_checkbox(array(
            'id' => '_pco_enable_custom_form',
            'label' => __('Włącz niestandardowy formularz zamówienia', 'papierotka-custom-order'),
            'description' => __('Zaznacz, aby włączyć niestandardowy formularz zamówienia dla tego produktu', 'papierotka-custom-order')
        ));
        
        // Kategorie dodatków
        $this->add_addon_category_fields('koperta', 'Koperta');
        $this->add_addon_category_fields('personalizacja_koperty', 'Personalizacja koperty');
        $this->add_addon_category_fields('lak', 'Lak');
        $this->add_addon_category_fields('karty_zaproszenia', 'Karty zaproszenia');
        $this->add_addon_category_fields('zlocenia', 'Złocenia');
        $this->add_addon_category_fields('wstazka_sznurek', 'Wstążka / Sznurek');
        $this->add_addon_category_fields('opaska', 'Opaska');
        $this->add_addon_category_fields('dodatki', 'Dodatki');
        
        echo '</div>';
    }
    
    /**
     * Dodanie pól dla kategorii dodatków
     */
    private function add_addon_category_fields($category_id, $category_name) {
        woocommerce_wp_checkbox(array(
            'id' => '_pco_enable_' . $category_id,
            'label' => sprintf(__('Włącz kategorię "%s"', 'papierotka-custom-order'), $category_name),
            'description' => sprintf(__('Zaznacz, aby włączyć kategorię dodatków "%s"', 'papierotka-custom-order'), $category_name)
        ));
        
        woocommerce_wp_checkbox(array(
            'id' => '_pco_multiselect_' . $category_id,
            'label' => sprintf(__('Multiselect dla "%s"', 'papierotka-custom-order'), $category_name),
            'description' => sprintf(__('Zaznacz, aby umożliwić wybór wielu opcji w kategorii "%s"', 'papierotka-custom-order'), $category_name)
        ));
        
        woocommerce_wp_textarea_input(array(
            'id' => '_pco_options_' . $category_id,
            'label' => sprintf(__('Opcje dla "%s"', 'papierotka-custom-order'), $category_name),
            'description' => __('Wprowadź opcje w formacie: "Nazwa opcji | Cena". Każda opcja w nowej linii.', 'papierotka-custom-order'),
            'placeholder' => __('Nazwa opcji | Cena', 'papierotka-custom-order'),
            'rows' => 5
        ));
    }
    
    /**
     * Zapisanie opcji produktu
     */
    public function save_product_options($post_id) {
        // Zapisanie głównej opcji
        $enable_custom_form = isset($_POST['_pco_enable_custom_form']) ? 'yes' : 'no';
        update_post_meta($post_id, '_pco_enable_custom_form', $enable_custom_form);
        
        // Zapisanie opcji dla kategorii
        $categories = array(
            'koperta', 'personalizacja_koperty', 'lak', 'karty_zaproszenia',
            'zlocenia', 'wstazka_sznurek', 'opaska', 'dodatki'
        );
        
        foreach ($categories as $category) {
            $enable_field = '_pco_enable_' . $category;
            $multiselect_field = '_pco_multiselect_' . $category;
            $options_field = '_pco_options_' . $category;
            
            $enable_value = isset($_POST[$enable_field]) ? 'yes' : 'no';
            $multiselect_value = isset($_POST[$multiselect_field]) ? 'yes' : 'no';
            $options_value = isset($_POST[$options_field]) ? sanitize_textarea_field($_POST[$options_field]) : '';
            
            update_post_meta($post_id, $enable_field, $enable_value);
            update_post_meta($post_id, $multiselect_field, $multiselect_value);
            update_post_meta($post_id, $options_field, $options_value);
        }
    }
    
    /**
     * Wyświetlenie opcji na stronie produktu
     */
    public function display_product_options() {
        global $product;
        
        // Sprawdzenie czy niestandardowy formularz jest włączony dla tego produktu
        $enable_custom_form = get_post_meta($product->get_id(), '_pco_enable_custom_form', true);
        if ($enable_custom_form !== 'yes') {
            return;
        }
        
        // Kategorie dodatków
        $categories = array(
            'koperta' => 'Koperta',
            'personalizacja_koperty' => 'Personalizacja koperty',
            'lak' => 'Lak',
            'karty_zaproszenia' => 'Karty zaproszenia',
            'zlocenia' => 'Złocenia',
            'wstazka_sznurek' => 'Wstążka / Sznurek',
            'opaska' => 'Opaska',
            'dodatki' => 'Dodatki'
        );
        
        echo '<div class="pco-product-options">';
        echo '<h3>' . __('Opcje zaproszenia', 'papierotka-custom-order') . '</h3>';
        
        foreach ($categories as $category_id => $category_name) {
            $enable_category = get_post_meta($product->get_id(), '_pco_enable_' . $category_id, true);
            if ($enable_category !== 'yes') {
                continue;
            }
            
            $multiselect = get_post_meta($product->get_id(), '_pco_multiselect_' . $category_id, true) === 'yes';
            $options_text = get_post_meta($product->get_id(), '_pco_options_' . $category_id, true);
            $options = $this->parse_options($options_text);
            
            if (empty($options)) {
                continue;
            }
            
            // Sprawdzamy, czy to pierwsza kategoria, aby domyślnie ją rozwinąć
            $is_first = $category_id === array_key_first(array_filter($categories, function($key) use ($product) {
                return get_post_meta($product->get_id(), '_pco_enable_' . $key, true) === 'yes';
            }, ARRAY_FILTER_USE_KEY));
            
            // Początek kontenera kategorii z akordeonem
            echo '<div class="pco-option-category pco-accordion" data-category="' . esc_attr($category_id) . '">';
            
            // Nagłówek akordeonu z ikoną rozwijania/zwijania
            echo '<div class="pco-accordion-header' . ($is_first ? ' pco-active' : '') . '">';
            echo '<h4>' . esc_html($category_name) . '</h4>';
            echo '<span class="pco-accordion-icon"></span>'; // Ikona plus/minus
            echo '</div>';
            
            // Zawartość akordeonu - domyślnie rozwinięta tylko dla pierwszej kategorii
            echo '<div class="pco-accordion-content' . ($is_first ? ' pco-show' : '') . '">';
            
            if ($multiselect) {
                // Dla opcji wielokrotnego wyboru używamy siatki (grid)
                echo '<div class="pco-option-items pco-multiselect pco-option-grid">';
                foreach ($options as $option) {
                    echo '<div class="pco-option-item">';
                    echo '<label>';
                    echo '<input type="checkbox" name="pco_' . esc_attr($category_id) . '[]" value="' . esc_attr($option['name']) . '" data-price="' . esc_attr($option['price']) . '" class="pco-option-input">';
                    echo esc_html($option['name']);
                    if ($option['price'] > 0) {
                        echo ' <span class="pco-option-price">(+' . wc_price($option['price']) . ')</span>';
                    }
                    echo '</label>';
                    echo '</div>';
                }
                echo '</div>';
            } else {
                echo '<div class="pco-option-items">';
                echo '<select name="pco_' . esc_attr($category_id) . '" class="pco-option-select">';
                echo '<option value="">' . __('Wybierz opcję', 'papierotka-custom-order') . '</option>';
                foreach ($options as $option) {
                    // Używamy pełnej nazwy opcji jako wartości
                    $option_name = esc_attr($option['name']);
                    echo '<option value="' . $option_name . '" data-price="' . esc_attr($option['price']) . '">';
                    echo esc_html($option['name']);
                    if ($option['price'] > 0) {
                        echo ' (+' . wc_price($option['price']) . ')';
                    }
                    echo '</option>';
                }
                echo '</select>';
                echo '</div>';
            }
            
            echo '</div>'; // Zamknięcie pco-accordion-content
            echo '</div>'; // Zamknięcie pco-option-category
        }
        
        // Podsumowanie ceny
        echo '<div class="pco-price-summary">';
        echo '<h4>' . __('Podsumowanie', 'papierotka-custom-order') . '</h4>';
        echo '<div class="pco-base-price-wrapper"><span class="pco-price-label">' . __('Cena podstawowa:', 'papierotka-custom-order') . '</span> <span class="pco-base-price">' . wc_price($product->get_price()) . '</span></div>';
        echo '<div class="pco-options-price-wrapper"><span class="pco-price-label">' . __('Cena dodatków:', 'papierotka-custom-order') . '</span> <span class="pco-options-price">' . wc_price(0) . '</span></div>';
        echo '<div class="pco-total-price-wrapper"><span class="pco-price-label">' . __('Cena za 1 zaproszenie:', 'papierotka-custom-order') . '</span> <span class="pco-total-price">' . wc_price($product->get_price()) . '</span></div>';
        
        // Dodanie sekcji wybranych opcji
        echo '<div class="pco-selected-options">';
        echo '<h5>' . __('Wybrane opcje:', 'papierotka-custom-order') . '</h5>';
        echo '<div class="pco-selected-options-list"></div>';
        echo '</div>';
        
        echo '<div class="pco-summary-section">';
        echo '<div class="pco-summary-line">' . __('Ilość:', 'papierotka-custom-order') . ' <span class="pco-summary-quantity">1</span></div>';
        echo '<div class="pco-summary-line">' . __('Cena jednostkowa:', 'papierotka-custom-order') . ' <span class="pco-summary-unit-price-wrapper">' . wc_price($product->get_price()) . '</span></div>';
        echo '<div class="pco-summary-line pco-summary-total">' . __('Cena łączna:', 'papierotka-custom-order') . ' <span class="pco-summary-total-price-wrapper">' . wc_price($product->get_price()) . '</span></div>';
        echo '</div>';
        echo '</div>';
        
        echo '</div>'; 
        // Dodanie ukrytych pól
        echo '<input type="hidden" name="pco_options_data" id="pco-options-data" value="">';
        echo '<input type="hidden" name="pco_total_price" id="pco-total-price" value="' . esc_attr($product->get_price()) . '">';
        echo '<input type="hidden" name="pco_redirect_to_form" value="1">';
    }
    
    /**
     * Parsowanie opcji z tekstu
     */
    private function parse_options($options_text) {
        $options = array();
        $lines = explode("\n", $options_text);
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }
            
            $parts = explode('|', $line);
            if (count($parts) !== 2) {
                continue;
            }
            
            $name = trim($parts[0]);
            $price = floatval(trim($parts[1]));
            
            $options[] = array(
                'name' => $name,
                'price' => $price
            );
        }
        
        return $options;
    }
    
    /**
     * Obsługa AJAX dla aktualizacji ceny
     */
    public function ajax_update_price() {
        check_ajax_referer('pco-ajax-nonce', 'nonce');
        
        $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
        $options = isset($_POST['options']) ? $_POST['options'] : array();
        $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
        
        if ($product_id <= 0) {
            wp_send_json_error(array('message' => __('Nieprawidłowy identyfikator produktu', 'papierotka-custom-order')));
            return;
        }
        
        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error(array('message' => __('Produkt nie istnieje', 'papierotka-custom-order')));
            return;
        }
        
        $base_price = floatval($product->get_price());
        $options_price = 0;
        
        // Obliczenie ceny opcji
        foreach ($options as $category => $selected_options) {
            if (empty($selected_options)) {
                continue;
            }
            
            if (is_array($selected_options)) {
                // Dla multiselect
                foreach ($selected_options as $option) {
                    $options_price += floatval($option['price']);
                }
            } else {
                // Dla pojedynczego wyboru
                $options_price += floatval($selected_options['price']);
            }
        }
        
        $total_price = $base_price + $options_price;
        $total_price_with_quantity = $total_price * $quantity;
        
        wp_send_json_success(array(
            'base_price' => $base_price,
            'options_price' => $options_price,
            'total_price' => $total_price,
            'total_price_with_quantity' => $total_price_with_quantity,
            'formatted_base_price' => wc_price($base_price),
            'formatted_options_price' => wc_price($options_price),
            'formatted_total_price' => wc_price($total_price),
            'formatted_total_price_with_quantity' => wc_price($total_price_with_quantity)
        ));
    }
}