jQuery(document).ready(function ($) {
    // Sprawdzenie czy pco_data jest dostępne
    if (typeof pco_data === 'undefined') {
        console.error('PCO: pco_data nie jest zdefiniowane - skrypt nie zostanie zainicjalizowany');
        return;
    }

    // Elementy DOM z bezpiecznym dostępem
    var $form = $('form.cart');
    var $quantityInput = $form.find('input.qty');
    var $optionsData = $form.find('#pco-options-data');
    var $totalPriceInput = $form.find('#pco-total-price');
    var $summaryQuantity = $('.pco-summary-quantity');
    var $summaryUnitPrice = $('.pco-summary-unit-price-wrapper');
    var $summaryTotalPrice = $('.pco-summary-total-price-wrapper');
    var $basePrice = $('.pco-base-price');
    var $optionsPrice = $('.pco-options-price');
    var $totalPrice = $('.pco-total-price');
    var $selectedOptionsList = $('.pco-selected-options-list');

    // Sprawdzenie czy formularz istnieje
    if ($form.length === 0) {
        console.log('PCO: Formularz WooCommerce nie został znaleziony - prawdopodobnie jesteśmy na stronie formularza zamówienia');
        return;
    }

    // Dane produktu z bezpiecznym dostępem
    var productId = $('input[name="product_id"]').val() || $('button.single_add_to_cart_button').val();
    var basePrice = 0;

    if ($basePrice.length > 0 && $basePrice.text()) {
        basePrice = parseFloat($basePrice.text().replace(/[^\d,.]/g, '').replace(',', '.')) || 0;
    }

    console.log('PCO: Inicjalizacja skryptu - Product ID:', productId, 'Base Price:', basePrice);

    // Inicjalizacja tylko jeśli mamy wymagane elementy
    if (productId && $quantityInput.length > 0) {
        updatePrice();
    } else {
        console.log('PCO: Brak wymaganych elementów do inicjalizacji aktualizacji ceny');
    }

    // Obsługa zmiany ilości
    $quantityInput.on('change', function () {
        updatePrice();
    });

    // Obsługa przycisków plus/minus ilości
    $(document).on('click', '.quantity .plus, .quantity .minus', function () {
        // Poczekaj na aktualizację pola ilości
        setTimeout(function () {
            updatePrice();
        }, 100);
    });

    // Obsługa zmiany opcji
    $('.pco-option-input, .pco-option-select').on('change', function () {
        updatePrice();
    });

    // Obsługa przycisku "Złóż zamówienie"
    $form.on('submit', function (e) {
        // Sprawdzenie czy to jest niestandardowy formularz
        if ($('input[name="pco_redirect_to_form"]').length) {
            // Aktualizacja danych opcji przed wysłaniem
            updateOptionsData();
        }
    });

    // Obsługa akordeonu
    $('.pco-accordion-header').on('click', function () {
        var $header = $(this);
        var $content = $header.next('.pco-accordion-content');
        var $category = $header.parent('.pco-accordion');

        // Przełączanie stanu aktywnego nagłówka
        $header.toggleClass('pco-active');

        // Przełączanie widoczności zawartości z animacją
        $content.slideToggle(300, function () {
            $content.toggleClass('pco-show');
        });

        // Opcjonalnie: zamykanie innych otwartych akordeonów
        // Odkomentuj poniższe linie, jeśli chcesz, aby tylko jedna kategoria była otwarta na raz
        // $('.pco-accordion-header').not($header).removeClass('pco-active');
        // $('.pco-accordion-content').not($content).slideUp(300).removeClass('pco-show');
    });

    // Funkcja aktualizująca cenę
    function updatePrice() {
        // Sprawdzenie czy mamy wymagane elementy
        if ($quantityInput.length === 0) {
            console.log('PCO: Brak pola ilości - pomijanie aktualizacji ceny');
            return;
        }

        var quantity = parseInt($quantityInput.val()) || 1;
        var options = collectOptions();

        // Aktualizacja danych opcji
        updateOptionsData();

        // Aktualizacja listy wybranych opcji
        updateSelectedOptionsList(options);

        // Sprawdzenie czy mamy dostęp do pco_data
        if (typeof pco_data === 'undefined' || !pco_data.ajax_url || !pco_data.nonce) {
            console.error('PCO: Brak wymaganych danych pco_data dla AJAX');
            return;
        }

        // Wywołanie AJAX do obliczenia ceny
        $.ajax({
            url: pco_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pco_update_price',
                nonce: pco_data.nonce,
                product_id: productId,
                options: options,
                quantity: quantity
            },
            success: function (response) {
                try {
                    if (response && response.success && response.data) {
                        // Aktualizacja wyświetlanych cen z bezpiecznym dostępem
                        if ($basePrice.length > 0 && response.data.formatted_base_price) {
                            $basePrice.html(response.data.formatted_base_price);
                        }
                        if ($optionsPrice.length > 0 && response.data.formatted_options_price) {
                            $optionsPrice.html(response.data.formatted_options_price);
                        }
                        if ($totalPrice.length > 0 && response.data.formatted_total_price) {
                            $totalPrice.html(response.data.formatted_total_price);
                        }
                        if ($summaryQuantity.length > 0) {
                            $summaryQuantity.text(quantity);
                        }
                        if ($summaryUnitPrice.length > 0 && response.data.formatted_total_price) {
                            $summaryUnitPrice.html(response.data.formatted_total_price);
                        }
                        if ($summaryTotalPrice.length > 0 && response.data.formatted_total_price_with_quantity) {
                            $summaryTotalPrice.html(response.data.formatted_total_price_with_quantity);
                        }

                        // Aktualizacja ukrytego pola z ceną
                        if ($totalPriceInput.length > 0 && response.data.total_price !== undefined) {
                            $totalPriceInput.val(response.data.total_price);
                        }
                    } else {
                        console.log('PCO: Nieprawidłowa odpowiedź z serwera:', response);
                    }
                } catch (error) {
                    console.error('PCO: Błąd podczas przetwarzania odpowiedzi aktualizacji ceny:', error);
                }
            },
            error: function(xhr, status, error) {
                console.error('PCO: Błąd AJAX podczas aktualizacji ceny:', status, error);
            }
        });
    }

    // Funkcja zbierająca wybrane opcje
    function collectOptions() {
        var options = {};

        // Przetwarzanie checkboxów (multiselect)
        $('.pco-option-category').each(function () {
            var category = $(this).data('category');
            var $multiselect = $(this).find('.pco-multiselect');

            if ($multiselect.length) {
                // Dla multiselect
                var selectedOptions = [];
                $multiselect.find('input:checked').each(function () {
                    var optionName = $(this).val();
                    selectedOptions.push({
                        name: optionName,
                        price: parseFloat($(this).data('price'))
                    });
                });

                if (selectedOptions.length) {
                    options[category] = selectedOptions;
                }
            } else {
                // Dla pojedynczego wyboru
                var $select = $(this).find('.pco-option-select');
                var selectedValue = $select.val();

                if (selectedValue && selectedValue.trim() !== '') {
                    // Pobieramy pełną nazwę opcji z tekstu wybranej opcji (bez ceny)
                    var selectedOption = $select.find('option:selected');
                    var fullName = selectedOption.text().split(' (+')[0].trim();

                    // Upewniamy się, że używamy pełnej nazwy opcji
                    options[category] = fullName;
                }
            }
        });

        return options;
    }

    // Funkcja aktualizująca ukryte pole z danymi opcji
    function updateOptionsData() {
        // Sprawdzenie czy pole opcji istnieje
        if ($optionsData.length === 0) {
            console.log('PCO: Pole opcji nie istnieje - pomijanie aktualizacji');
            return;
        }

        try {
            var options = collectOptions();

            // Używamy JSON.stringify do serializacji danych opcji
            // Użyj encodeURIComponent dla zapewnienia poprawnego kodowania UTF-8
            $optionsData.val(JSON.stringify(options));
        } catch (error) {
            console.error('PCO: Błąd podczas aktualizacji danych opcji:', error);
        }
    }

    // Funkcja aktualizująca listę wybranych opcji
    function updateSelectedOptionsList(options) {
        var html = '';
        var hasOptions = false;

        // Pobierz nazwy kategorii
        var categoryNames = {
            'koperta': 'Koperta',
            'personalizacja_koperty': 'Personalizacja koperty',
            'wklejka': 'Wklejka',
            'personalizacja_wklejki': 'Personalizacja wklejki',
            'dodatki_do_wklejki': 'Dodatki do wklejki',
            'lak': 'Lak',
            'dodatki': 'Dodatki'
        };

        $.each(options, function (category, selectedOptions) {
            if (($.isArray(selectedOptions) && selectedOptions.length > 0) || (!$.isArray(selectedOptions) && selectedOptions)) {
                hasOptions = true;
                var categoryName = categoryNames[category] || category;
                html += '<div class="pco-selected-option-category">';
                html += '<span class="pco-selected-option-category-name">' + categoryName + ':</span> ';

                if ($.isArray(selectedOptions)) {
                    var optionNames = [];
                    $.each(selectedOptions, function (index, option) {
                        optionNames.push(option.name);
                    });
                    html += optionNames.join(', ');
                } else {
                    // Używamy pełnej nazwy opcji bez ceny
                    html += selectedOptions;
                }

                html += '</div>';
            }
        });

        if (hasOptions) {
            $selectedOptionsList.html(html).parent().show();
        } else {
            $selectedOptionsList.html('').parent().hide();
        }
    }
});