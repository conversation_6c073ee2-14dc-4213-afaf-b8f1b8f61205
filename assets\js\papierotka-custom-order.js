jQuery(document).ready(function ($) {
    // Elementy DOM
    var $form = $('form.cart');
    var $quantityInput = $form.find('input.qty');
    var $optionsData = $form.find('#pco-options-data');
    var $totalPriceInput = $form.find('#pco-total-price');
    var $summaryQuantity = $('.pco-summary-quantity');
    var $summaryUnitPrice = $('.pco-summary-unit-price-wrapper');
    var $summaryTotalPrice = $('.pco-summary-total-price-wrapper');
    var $basePrice = $('.pco-base-price');
    var $optionsPrice = $('.pco-options-price');
    var $totalPrice = $('.pco-total-price');
    var $selectedOptionsList = $('.pco-selected-options-list');

    // Dane produktu
    var productId = $('input[name="product_id"]').val() || $('button.single_add_to_cart_button').val();
    var basePrice = parseFloat($basePrice.text().replace(/[^\d,.]/g, '').replace(',', '.'));

    // Inicjalizacja
    updatePrice();

    // Obsługa zmiany ilości
    $quantityInput.on('change', function () {
        updatePrice();
    });

    // Obsługa przycisków plus/minus ilości
    $(document).on('click', '.quantity .plus, .quantity .minus', function () {
        // Poczekaj na aktualizację pola ilości
        setTimeout(function () {
            updatePrice();
        }, 100);
    });

    // Obsługa zmiany opcji
    $('.pco-option-input, .pco-option-select').on('change', function () {
        updatePrice();
    });

    // Obsługa przycisku "Złóż zamówienie"
    $form.on('submit', function (e) {
        // Sprawdzenie czy to jest niestandardowy formularz
        if ($('input[name="pco_redirect_to_form"]').length) {
            // Aktualizacja danych opcji przed wysłaniem
            updateOptionsData();
        }
    });

    // Obsługa akordeonu
    $('.pco-accordion-header').on('click', function () {
        var $header = $(this);
        var $content = $header.next('.pco-accordion-content');
        var $category = $header.parent('.pco-accordion');

        // Przełączanie stanu aktywnego nagłówka
        $header.toggleClass('pco-active');

        // Przełączanie widoczności zawartości z animacją
        $content.slideToggle(300, function () {
            $content.toggleClass('pco-show');
        });

        // Opcjonalnie: zamykanie innych otwartych akordeonów
        // Odkomentuj poniższe linie, jeśli chcesz, aby tylko jedna kategoria była otwarta na raz
        // $('.pco-accordion-header').not($header).removeClass('pco-active');
        // $('.pco-accordion-content').not($content).slideUp(300).removeClass('pco-show');
    });

    // Funkcja aktualizująca cenę
    function updatePrice() {
        var quantity = parseInt($quantityInput.val());
        var options = collectOptions();

        // Aktualizacja danych opcji
        updateOptionsData();

        // Aktualizacja listy wybranych opcji
        updateSelectedOptionsList(options);

        // Wywołanie AJAX do obliczenia ceny
        $.ajax({
            url: pco_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pco_update_price',
                nonce: pco_data.nonce,
                product_id: productId,
                options: options,
                quantity: quantity
            },
            success: function (response) {
                if (response.success) {
                    // Aktualizacja wyświetlanych cen
                    $basePrice.html(response.data.formatted_base_price);
                    $optionsPrice.html(response.data.formatted_options_price);
                    $totalPrice.html(response.data.formatted_total_price);
                    $summaryQuantity.text(quantity);
                    $summaryUnitPrice.html(response.data.formatted_total_price);
                    $summaryTotalPrice.html(response.data.formatted_total_price_with_quantity);

                    // Aktualizacja ukrytego pola z ceną
                    $totalPriceInput.val(response.data.total_price);
                }
            }
        });
    }

    // Funkcja zbierająca wybrane opcje
    function collectOptions() {
        var options = {};
        
        // Przetwarzanie checkboxów (multiselect)
        $('.pco-option-category').each(function () {
            var category = $(this).data('category');
            var $multiselect = $(this).find('.pco-multiselect');

            if ($multiselect.length) {
                // Dla multiselect
                var selectedOptions = [];
                $multiselect.find('input:checked').each(function () {
                    var optionName = $(this).val();
                    selectedOptions.push({
                        name: optionName,
                        price: parseFloat($(this).data('price'))
                    });
                });

                if (selectedOptions.length) {
                    options[category] = selectedOptions;
                }
            } else {
                // Dla pojedynczego wyboru
                var $select = $(this).find('.pco-option-select');
                var selectedValue = $select.val();
                
                if (selectedValue && selectedValue.trim() !== '') {
                    // Pobieramy pełną nazwę opcji z tekstu wybranej opcji (bez ceny)
                    var selectedOption = $select.find('option:selected');
                    var fullName = selectedOption.text().split(' (+')[0].trim();
                    
                    // Upewniamy się, że używamy pełnej nazwy opcji
                    options[category] = fullName;
                }
            }
        });

        return options;
    }

    // Funkcja aktualizująca ukryte pole z danymi opcji
    function updateOptionsData() {
        var options = collectOptions();
        
        // Używamy JSON.stringify do serializacji danych opcji
        // Użyj encodeURIComponent dla zapewnienia poprawnego kodowania UTF-8
        $optionsData.val(JSON.stringify(options));
    }

    // Funkcja aktualizująca listę wybranych opcji
    function updateSelectedOptionsList(options) {
        var html = '';
        var hasOptions = false;

        // Pobierz nazwy kategorii
        var categoryNames = {
            'koperta': 'Koperta',
            'personalizacja_koperty': 'Personalizacja koperty',
            'wklejka': 'Wklejka',
            'personalizacja_wklejki': 'Personalizacja wklejki',
            'dodatki_do_wklejki': 'Dodatki do wklejki',
            'lak': 'Lak',
            'dodatki': 'Dodatki'
        };

        $.each(options, function (category, selectedOptions) {
            if (($.isArray(selectedOptions) && selectedOptions.length > 0) || (!$.isArray(selectedOptions) && selectedOptions)) {
                hasOptions = true;
                var categoryName = categoryNames[category] || category;
                html += '<div class="pco-selected-option-category">';
                html += '<span class="pco-selected-option-category-name">' + categoryName + ':</span> ';

                if ($.isArray(selectedOptions)) {
                    var optionNames = [];
                    $.each(selectedOptions, function (index, option) {
                        optionNames.push(option.name);
                    });
                    html += optionNames.join(', ');
                } else {
                    // Używamy pełnej nazwy opcji bez ceny
                    html += selectedOptions;
                }

                html += '</div>';
            }
        });

        if (hasOptions) {
            $selectedOptionsList.html(html).parent().show();
        } else {
            $selectedOptionsList.html('').parent().hide();
        }
    }
});