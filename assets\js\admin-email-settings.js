/**
 * JavaScript for Email Settings Admin Page
 */

jQuery(document).ready(function($) {
    
    // Zmienna do przechowywania aktualnie aktywnego edytora
    var currentEditor = null;
    
    /**
     * Wstawienie zmiennej do aktywnego edytora
     */
    window.insertVariable = function(variable) {
        // Sprawdzenie czy TinyMCE jest aktywny
        if (typeof tinymce !== 'undefined') {
            // Próba znalezienia aktywnego edytora
            var activeEditor = tinymce.activeEditor;
            
            if (activeEditor && !activeEditor.isHidden()) {
                activeEditor.insertContent(variable);
                return;
            }
            
            // Jeśli nie ma aktywnego edytora, spróbuj z ostatnio używanym
            if (currentEditor && !tinymce.get(currentEditor).isHidden()) {
                tinymce.get(currentEditor).insertContent(variable);
                return;
            }
        }
        
        // Fallback - wstawienie do textarea
        var $activeTextarea = $('textarea:focus');
        if ($activeTextarea.length > 0) {
            insertAtCursor($activeTextarea[0], variable);
        } else {
            // Jeśli nie ma aktywnego textarea, wstaw do pierwszego dostępnego
            var $firstTextarea = $('.pco-template-section textarea').first();
            if ($firstTextarea.length > 0) {
                insertAtCursor($firstTextarea[0], variable);
                $firstTextarea.focus();
            }
        }
    };
    
    /**
     * Wstawienie tekstu w pozycji kursora w textarea
     */
    function insertAtCursor(textarea, text) {
        var startPos = textarea.selectionStart;
        var endPos = textarea.selectionEnd;
        var value = textarea.value;
        
        textarea.value = value.substring(0, startPos) + text + value.substring(endPos, value.length);
        textarea.selectionStart = textarea.selectionEnd = startPos + text.length;
        
        // Wywołanie zdarzenia change
        $(textarea).trigger('change');
    }
    
    /**
     * Śledzenie aktywnego edytora TinyMCE
     */
    if (typeof tinymce !== 'undefined') {
        $(document).on('tinymce-editor-init', function(event, editor) {
            editor.on('focus', function() {
                currentEditor = editor.id;
            });
        });
    }
    
    /**
     * Śledzenie aktywnego textarea
     */
    $('.pco-template-section textarea').on('focus', function() {
        currentEditor = this.id;
    });
    
    /**
     * Podgląd e-maila
     */
    window.previewEmail = function(type) {
        var subject, content;
        
        if (type === 'admin') {
            subject = $('#admin_subject').val();
            // Pobranie zawartości z TinyMCE lub textarea
            if (typeof tinymce !== 'undefined' && tinymce.get('admin_content')) {
                content = tinymce.get('admin_content').getContent();
            } else {
                content = $('#admin_content').val();
            }
        } else {
            subject = $('#customer_subject').val();
            // Pobranie zawartości z TinyMCE lub textarea
            if (typeof tinymce !== 'undefined' && tinymce.get('customer_content')) {
                content = tinymce.get('customer_content').getContent();
            } else {
                content = $('#customer_content').val();
            }
        }
        
        // Przykładowe dane do podglądu
        var sampleData = {
            order_id: 'PCO-20250101-12345',
            customer_name: 'Jan Kowalski',
            customer_email: '<EMAIL>',
            customer_phone: '+48 123 456 789',
            bride_name: 'Anna',
            groom_name: 'Jan',
            wedding_date: '2025-06-15',
            wedding_time: '15:00',
            product_name: 'Eleganckie zaproszenia ślubne',
            quantity: '50',
            total_price: '750.00',
            order_details: '<h3>Szczegółowe dane zamówienia:</h3><ul><li><strong>Nazwa kościoła:</strong> Kościół św. Jana</li><li><strong>Adres kościoła:</strong> ul. Kościelna 1, Warszawa</li></ul>',
            site_name: 'Papierotka',
            site_url: window.location.origin
        };
        
        // Zastąpienie zmiennych w temacie i treści
        var processedSubject = processTemplate(subject, sampleData);
        var processedContent = processTemplate(content, sampleData);
        
        // Utworzenie pełnego HTML e-maila
        var emailHtml = '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>' + 
                       processedSubject + '</title></head><body>' + processedContent + '</body></html>';
        
        // Wyświetlenie w modalu
        showEmailPreview(processedSubject, emailHtml);
    };
    
    /**
     * Przetworzenie szablonu z danymi
     */
    function processTemplate(template, data) {
        var processed = template;
        
        for (var key in data) {
            if (data.hasOwnProperty(key)) {
                var regex = new RegExp('\\{' + key + '\\}', 'g');
                processed = processed.replace(regex, data[key]);
            }
        }
        
        return processed;
    }
    
    /**
     * Wyświetlenie podglądu e-maila w modalu
     */
    function showEmailPreview(subject, content) {
        var modal = $('#pco-email-preview-modal');
        var previewContent = $('#pco-email-preview-content');
        
        // Utworzenie iframe dla bezpiecznego wyświetlenia HTML
        var iframe = $('<iframe></iframe>');
        iframe.on('load', function() {
            var iframeDoc = this.contentDocument || this.contentWindow.document;
            iframeDoc.open();
            iframeDoc.write(content);
            iframeDoc.close();
        });
        
        previewContent.html('<h3>Temat: ' + subject + '</h3>').append(iframe);
        modal.show();
    }
    
    /**
     * Zamknięcie modalu
     */
    $(document).on('click', '.pco-modal-close', function() {
        $('#pco-email-preview-modal').hide();
    });
    
    // Zamknięcie modalu po kliknięciu poza nim
    $(document).on('click', '#pco-email-preview-modal', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });
    
    // Zamknięcie modalu klawiszem ESC
    $(document).on('keydown', function(e) {
        if (e.keyCode === 27) { // ESC
            $('#pco-email-preview-modal').hide();
        }
    });
    
    /**
     * Walidacja formularza przed wysłaniem
     */
    $('form').on('submit', function(e) {
        var isValid = true;
        var errors = [];
        
        // Sprawdzenie czy tematy nie są puste
        if ($('#admin_subject').val().trim() === '') {
            errors.push('Temat wiadomości dla administratora nie może być pusty.');
            isValid = false;
        }
        
        if ($('#customer_subject').val().trim() === '') {
            errors.push('Temat wiadomości dla klienta nie może być pusty.');
            isValid = false;
        }
        
        // Sprawdzenie czy treści nie są puste
        var adminContent = '';
        if (typeof tinymce !== 'undefined' && tinymce.get('admin_content')) {
            adminContent = tinymce.get('admin_content').getContent();
        } else {
            adminContent = $('#admin_content').val();
        }
        
        if (adminContent.trim() === '') {
            errors.push('Treść wiadomości dla administratora nie może być pusta.');
            isValid = false;
        }
        
        var customerContent = '';
        if (typeof tinymce !== 'undefined' && tinymce.get('customer_content')) {
            customerContent = tinymce.get('customer_content').getContent();
        } else {
            customerContent = $('#customer_content').val();
        }
        
        if (customerContent.trim() === '') {
            errors.push('Treść wiadomości dla klienta nie może być pusta.');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('Błędy walidacji:\n\n' + errors.join('\n'));
            return false;
        }
        
        // Dodanie klasy loading
        $(this).addClass('pco-loading');
        $('input[type="submit"]').prop('disabled', true);
    });
    
    /**
     * Automatyczne zapisywanie w localStorage (opcjonalne)
     */
    var autoSaveInterval;
    
    function startAutoSave() {
        autoSaveInterval = setInterval(function() {
            var formData = {
                admin_subject: $('#admin_subject').val(),
                customer_subject: $('#customer_subject').val(),
                admin_enabled: $('#admin_enabled').is(':checked'),
                customer_enabled: $('#customer_enabled').is(':checked'),
                timestamp: new Date().getTime()
            };
            
            // Pobranie zawartości edytorów
            if (typeof tinymce !== 'undefined') {
                if (tinymce.get('admin_content')) {
                    formData.admin_content = tinymce.get('admin_content').getContent();
                }
                if (tinymce.get('customer_content')) {
                    formData.customer_content = tinymce.get('customer_content').getContent();
                }
            }
            
            localStorage.setItem('pco_email_settings_autosave', JSON.stringify(formData));
        }, 30000); // Zapisuj co 30 sekund
    }
    
    function loadAutoSave() {
        var saved = localStorage.getItem('pco_email_settings_autosave');
        if (saved) {
            try {
                var data = JSON.parse(saved);
                // Sprawdź czy dane nie są starsze niż 24 godziny
                if (new Date().getTime() - data.timestamp < 24 * 60 * 60 * 1000) {
                    if (confirm('Znaleziono automatycznie zapisane zmiany. Czy chcesz je przywrócić?')) {
                        $('#admin_subject').val(data.admin_subject || '');
                        $('#customer_subject').val(data.customer_subject || '');
                        $('#admin_enabled').prop('checked', data.admin_enabled || false);
                        $('#customer_enabled').prop('checked', data.customer_enabled || false);
                        
                        // Przywrócenie zawartości edytorów po ich inicjalizacji
                        $(document).on('tinymce-editor-init', function(event, editor) {
                            if (editor.id === 'admin_content' && data.admin_content) {
                                editor.setContent(data.admin_content);
                            }
                            if (editor.id === 'customer_content' && data.customer_content) {
                                editor.setContent(data.customer_content);
                            }
                        });
                    }
                }
            } catch (e) {
                console.log('Błąd podczas ładowania automatycznie zapisanych danych:', e);
            }
        }
    }
    
    // Uruchomienie automatycznego zapisywania
    startAutoSave();
    loadAutoSave();
    
    // Czyszczenie automatycznego zapisu po pomyślnym zapisaniu
    $('form').on('submit', function() {
        localStorage.removeItem('pco_email_settings_autosave');
        clearInterval(autoSaveInterval);
    });
});
