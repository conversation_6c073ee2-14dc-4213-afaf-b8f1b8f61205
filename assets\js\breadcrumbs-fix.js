/**
 * Skrypt do naprawy breadcrumbs na stronach zamówienia
 */
document.addEventListener('DOMContentLoaded', function() {
    // Pobieranie informacji o stronie z URL
    var currentUrl = window.location.href;
    var urlParts = currentUrl.split('/');
    var lastUrlPart = '';
    
    // Znajdź ostatnią niepustą część URL (pomijając parametry)
    for (var i = urlParts.length - 1; i >= 0; i--) {
        var part = urlParts[i].split('?')[0]; // Usunięcie parametrów
        if (part && part.trim() !== '') {
            lastUrlPart = part;
            break;
        }
    }
    
    // Określenie, na której stronie jesteśmy
    var isOrderConfirmationPage = currentUrl.indexOf('potwierdzenie-zamowienia') > -1;
    var isOrderFormPage = currentUrl.indexOf('zamowienie') > -1 && !isOrderConfirmationPage;
    
    // Tytuł strony zależny od kontekstu
    var pageTitle = '';
    
    // Określenie tytułu strony na podstawie URL
    if (isOrderConfirmationPage) {
        pageTitle = 'Potwierdzenie zamówienia';
    } else if (isOrderFormPage) {
        pageTitle = 'Zamówienie'; // Zmiana na "Zamówienie" zgodnie z URL
    } else if (lastUrlPart) {
        // Dla innych stron użyj ostatniej części URL
        // Zamień myślniki na spacje i ustaw pierwszą literę jako wielką
        pageTitle = lastUrlPart.replace(/-/g, ' ').replace(/\b\w/g, function(l) { 
            return l.toUpperCase(); 
        });
    }
    
    // Jeśli mamy tytuł strony, napraw breadcrumbs
    if (pageTitle) {
        console.log('Wykryto stronę: ' + pageTitle + ' na podstawie URL: ' + currentUrl);
        
        // Zmiana tytułu w breadcrumbs
        fixBreadcrumbs(pageTitle);
        
        // Dodatkowa funkcja wykonywana z opóźnieniem (dla elementów ładowanych dynamicznie)
        setTimeout(function() {
            fixBreadcrumbs(pageTitle);
        }, 500);
    }
    
    /**
     * Funkcja naprawiająca breadcrumbs
     */
    function fixBreadcrumbs(pageTitle) {
        console.log('Naprawianie breadcrumbs - zmiana "Blog" na "' + pageTitle + '"');
        
        // Próba 1: Bezpośrednie celowanie w elementy breadcrumbs
        var breadcrumbs = document.querySelectorAll('.breadcrumbs, .breadcrumb, .breadcrumb-trail, .rank-math-breadcrumb, .yoast-breadcrumb, .woocommerce-breadcrumb');
        breadcrumbs.forEach(function(breadcrumb) {
            console.log('Znaleziono element breadcrumbs:', breadcrumb);
            
            // Próba 1.1: Zamiana tekstu w linkach
            breadcrumb.querySelectorAll('a').forEach(function(link) {
                if (link.textContent.trim() === 'Blog') {
                    console.log('Znaleziono link z tekstem "Blog"');
                    link.textContent = pageTitle;
                }
            });
            
            // Próba 1.2: Zamiana tekstu w elementach span
            breadcrumb.querySelectorAll('span').forEach(function(span) {
                if (span.textContent.trim() === 'Blog') {
                    console.log('Znaleziono span z tekstem "Blog"');
                    span.textContent = pageTitle;
                }
            });
            
            // Próba 1.3: Zamiana tekstu w elementach li
            breadcrumb.querySelectorAll('li').forEach(function(li) {
                if (li.textContent.trim() === 'Blog') {
                    console.log('Znaleziono li z tekstem "Blog"');
                    li.textContent = pageTitle;
                }
            });
            
            // Próba 1.4: Bezpośrednia modyfikacja HTML
            var html = breadcrumb.innerHTML;
            if (html.indexOf('>Blog<') > -1) {
                console.log('Znaleziono tekst ">Blog<" w HTML');
                breadcrumb.innerHTML = html.replace(/>Blog</g, '>' + pageTitle + '<');
            }
        });
        
        // Próba 2: Celowanie w konkretne elementy na stronie
        document.querySelectorAll('.entry-header, .page-header').forEach(function(header) {
            header.querySelectorAll('a, span, div').forEach(function(element) {
                if (element.textContent.trim() === 'Blog') {
                    console.log('Znaleziono element w nagłówku z tekstem "Blog"');
                    element.textContent = pageTitle;
                }
            });
        });
        
        // Próba 3: Szukanie tekstu "Home / Blog"
        document.querySelectorAll('*').forEach(function(element) {
            if (element.childNodes && element.childNodes.length === 1 && element.childNodes[0].nodeType === 3) {
                // Element zawiera tylko tekst
                var text = element.textContent.trim();
                if (text === 'Home / Blog') {
                    console.log('Znaleziono tekst "Home / Blog"');
                    element.textContent = 'Home / ' + pageTitle;
                }
            }
        });
        
        // Próba 4: Bezpośrednie celowanie w widoczny element breadcrumbs na zrzucie ekranu
        // Szukamy elementu zawierającego tekst "Home / Blog" w konkretnym miejscu
        var breadcrumbsElement = document.querySelector('.breadcrumb, .breadcrumbs, nav.breadcrumb');
        if (!breadcrumbsElement) {
            // Jeśli nie znaleziono standardowych klas, szukamy po zawartości
            var allElements = document.querySelectorAll('div, nav, span, p');
            allElements.forEach(function(element) {
                if (element.textContent.trim() === 'Home / Blog') {
                    console.log('Znaleziono element zawierający dokładnie "Home / Blog"');
                    breadcrumbsElement = element;
                }
            });
        }
        
        if (breadcrumbsElement) {
            // Znaleziono element breadcrumbs
            console.log('Modyfikacja elementu breadcrumbs:', breadcrumbsElement);
            
            // Próba 4.1: Zamiana tekstu bezpośrednio
            if (breadcrumbsElement.textContent.trim() === 'Home / Blog') {
                breadcrumbsElement.textContent = 'Home / ' + pageTitle;
            }
            
            // Próba 4.2: Zamiana HTML
            var html = breadcrumbsElement.innerHTML;
            if (html.indexOf('Home / Blog') > -1) {
                breadcrumbsElement.innerHTML = html.replace('Home / Blog', 'Home / ' + pageTitle);
            }
            
            // Próba 4.3: Zamiana tekstu w węzłach tekstowych
            var walker = document.createTreeWalker(breadcrumbsElement, NodeFilter.SHOW_TEXT, null, false);
            var node;
            while (node = walker.nextNode()) {
                if (node.nodeValue.indexOf('Home / Blog') > -1) {
                    console.log('Znaleziono węzeł tekstowy zawierający "Home / Blog"');
                    node.nodeValue = node.nodeValue.replace('Home / Blog', 'Home / ' + pageTitle);
                } else if (node.nodeValue.trim() === 'Blog') {
                    console.log('Znaleziono węzeł tekstowy zawierający tylko "Blog"');
                    node.nodeValue = pageTitle;
                }
            }
        }
        
        // Próba 5: Ostateczna próba - szukanie po konkretnej strukturze widocznej na zrzucie ekranu
        var homeLinks = document.querySelectorAll('a[href="/"]');
        homeLinks.forEach(function(homeLink) {
            if (homeLink.textContent.trim() === 'Home') {
                // Znaleziono link "Home", szukamy następnego elementu
                var parent = homeLink.parentNode;
                var nextSibling = homeLink.nextSibling;
                while (nextSibling) {
                    if (nextSibling.nodeType === 3 && nextSibling.nodeValue.indexOf('/') > -1) {
                        // Znaleziono separator "/"
                        var nextElement = nextSibling.nextSibling;
                        while (nextElement) {
                            if (nextElement.nodeType === 1 && nextElement.textContent.trim() === 'Blog') {
                                console.log('Znaleziono element "Blog" po linku "Home" i separatorze "/"');
                                nextElement.textContent = pageTitle;
                                break;
                            } else if (nextElement.nodeType === 3 && nextElement.nodeValue.trim() === 'Blog') {
                                console.log('Znaleziono tekst "Blog" po linku "Home" i separatorze "/"');
                                nextElement.nodeValue = pageTitle;
                                break;
                            }
                            nextElement = nextElement.nextSibling;
                        }
                        break;
                    }
                    nextSibling = nextSibling.nextSibling;
                }
            }
        });
        
        // Dodatkowa poprawka dla nagłówków H1
        document.querySelectorAll('h1').forEach(function(h1) {
            // Jeśli nagłówek zawiera "Blog" lub niepoprawny tytuł
            if (h1.textContent.trim() === 'Blog' || 
                (h1.textContent.trim() === 'Potwierdzenie zamówienia' && pageTitle !== 'Potwierdzenie zamówienia') ||
                (h1.textContent.trim() === 'Formularz zamówienia' && pageTitle !== 'Formularz zamówienia')) {
                h1.textContent = pageTitle;
            }
        });
        
        // Aktualizacja tytułu strony w przeglądarce
        if (document.title.indexOf('Blog') > -1) {
            document.title = document.title.replace('Blog', pageTitle);
        }
    }
});
