<?php
/**
 * Narzędzia debugowania dla wtyczki
 */

if (!defined('ABSPATH')) {
    exit;
}

class PCO_Debug_Tools {

    public function __construct() {
        add_action('admin_menu', array($this, 'add_debug_menu'));
        add_action('wp_ajax_pco_test_database', array($this, 'test_database_connection'));
        add_action('wp_ajax_pco_recreate_table', array($this, 'recreate_table'));
        add_action('wp_ajax_pco_update_table_structure', array($this, 'update_table_structure'));
    }

    /**
     * Dodanie menu debugowania
     */
    public function add_debug_menu() {
        add_submenu_page(
            'woocommerce',
            __('PCO Debug', 'papierotka-custom-order'),
            __('PCO Debug', 'papierotka-custom-order'),
            'manage_woocommerce',
            'pco-debug',
            array($this, 'debug_page')
        );
    }

    /**
     * Strona debugowania
     */
    public function debug_page() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pco_orders';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        ?>
        <div class="wrap">
            <h1><?php _e('PCO Debug Tools', 'papierotka-custom-order'); ?></h1>

            <div class="card">
                <h2><?php _e('Status bazy danych', 'papierotka-custom-order'); ?></h2>
                <p><strong><?php _e('Nazwa tabeli:', 'papierotka-custom-order'); ?></strong> <?php echo esc_html($table_name); ?></p>
                <p><strong><?php _e('Tabela istnieje:', 'papierotka-custom-order'); ?></strong>
                    <span style="color: <?php echo $table_exists ? 'green' : 'red'; ?>">
                        <?php echo $table_exists ? __('TAK', 'papierotka-custom-order') : __('NIE', 'papierotka-custom-order'); ?>
                    </span>
                </p>

                <?php if ($table_exists): ?>
                    <?php
                    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
                    $structure = $wpdb->get_results("DESCRIBE $table_name");
                    ?>
                    <p><strong><?php _e('Liczba zamówień:', 'papierotka-custom-order'); ?></strong> <?php echo intval($count); ?></p>

                    <h3><?php _e('Struktura tabeli:', 'papierotka-custom-order'); ?></h3>
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Pole', 'papierotka-custom-order'); ?></th>
                                <th><?php _e('Typ', 'papierotka-custom-order'); ?></th>
                                <th><?php _e('Null', 'papierotka-custom-order'); ?></th>
                                <th><?php _e('Klucz', 'papierotka-custom-order'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($structure as $field): ?>
                                <tr>
                                    <td><?php echo esc_html($field->Field); ?></td>
                                    <td><?php echo esc_html($field->Type); ?></td>
                                    <td><?php echo esc_html($field->Null); ?></td>
                                    <td><?php echo esc_html($field->Key); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>

                <p>
                    <button type="button" class="button button-primary" onclick="testDatabase()">
                        <?php _e('Testuj połączenie z bazą danych', 'papierotka-custom-order'); ?>
                    </button>

                    <button type="button" class="button button-secondary" onclick="recreateTable()">
                        <?php _e('Odtwórz tabelę', 'papierotka-custom-order'); ?>
                    </button>

                    <button type="button" class="button" onclick="updateTableStructure()">
                        <?php _e('Aktualizuj strukturę tabeli', 'papierotka-custom-order'); ?>
                    </button>
                </p>

                <div id="debug-results"></div>
            </div>

            <div class="card">
                <h2><?php _e('Informacje systemowe', 'papierotka-custom-order'); ?></h2>
                <p><strong><?php _e('Wersja PHP:', 'papierotka-custom-order'); ?></strong> <?php echo PHP_VERSION; ?></p>
                <p><strong><?php _e('Wersja WordPress:', 'papierotka-custom-order'); ?></strong> <?php echo get_bloginfo('version'); ?></p>
                <p><strong><?php _e('Wersja WooCommerce:', 'papierotka-custom-order'); ?></strong> <?php echo defined('WC_VERSION') ? WC_VERSION : 'Nie zainstalowany'; ?></p>
                <p><strong><?php _e('Charset bazy danych:', 'papierotka-custom-order'); ?></strong> <?php echo $wpdb->charset; ?></p>
                <p><strong><?php _e('Collate bazy danych:', 'papierotka-custom-order'); ?></strong> <?php echo $wpdb->collate; ?></p>
                <p><strong><?php _e('WP_DEBUG:', 'papierotka-custom-order'); ?></strong> <?php echo defined('WP_DEBUG') && WP_DEBUG ? 'Włączony' : 'Wyłączony'; ?></p>
                <p><strong><?php _e('WP_DEBUG_LOG:', 'papierotka-custom-order'); ?></strong> <?php echo defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Włączony' : 'Wyłączony'; ?></p>
            </div>
        </div>

        <script>
        function testDatabase() {
            var button = event.target;
            var originalText = button.textContent;
            button.textContent = 'Testowanie...';
            button.disabled = true;

            jQuery.post(ajaxurl, {
                action: 'pco_test_database',
                nonce: '<?php echo wp_create_nonce('pco-debug-nonce'); ?>'
            }, function(response) {
                document.getElementById('debug-results').innerHTML = '<div class="notice notice-' +
                    (response.success ? 'success' : 'error') + '"><p>' + response.data.message + '</p></div>';
                button.textContent = originalText;
                button.disabled = false;
            });
        }

        function recreateTable() {
            if (!confirm('Czy na pewno chcesz odtworzyć tabelę? To usunie wszystkie istniejące zamówienia!')) {
                return;
            }

            var button = event.target;
            var originalText = button.textContent;
            button.textContent = 'Odtwarzanie...';
            button.disabled = true;

            jQuery.post(ajaxurl, {
                action: 'pco_recreate_table',
                nonce: '<?php echo wp_create_nonce('pco-debug-nonce'); ?>'
            }, function(response) {
                document.getElementById('debug-results').innerHTML = '<div class="notice notice-' +
                    (response.success ? 'success' : 'error') + '"><p>' + response.data.message + '</p></div>';
                button.textContent = originalText;
                button.disabled = false;

                if (response.success) {
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                }
            });
        }

        function updateTableStructure() {
            var button = event.target;
            var originalText = button.textContent;
            button.textContent = 'Aktualizowanie...';
            button.disabled = true;

            jQuery.post(ajaxurl, {
                action: 'pco_update_table_structure',
                nonce: '<?php echo wp_create_nonce('pco-debug-nonce'); ?>'
            }, function(response) {
                document.getElementById('debug-results').innerHTML = '<div class="notice notice-' +
                    (response.success ? 'success' : 'error') + '"><p>' + response.data.message + '</p></div>';
                button.textContent = originalText;
                button.disabled = false;

                if (response.success) {
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                }
            });
        }
        </script>
        <?php
    }

    /**
     * Test połączenia z bazą danych
     */
    public function test_database_connection() {
        if (!wp_verify_nonce($_POST['nonce'], 'pco-debug-nonce')) {
            wp_send_json_error(array('message' => 'Błąd bezpieczeństwa'));
            return;
        }

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(array('message' => 'Brak uprawnień'));
            return;
        }

        global $wpdb;

        try {
            // Test podstawowego połączenia
            $result = $wpdb->get_var("SELECT 1");

            if ($result != 1) {
                wp_send_json_error(array('message' => 'Błąd połączenia z bazą danych'));
                return;
            }

            // Test zapisu z polskimi znakami
            $test_data = array(
                'test_field' => 'Test polskich znaków: ąćęłńóśźż ĄĆĘŁŃÓŚŹŻ'
            );

            $json_test = json_encode($test_data, JSON_UNESCAPED_UNICODE);

            if ($json_test === false) {
                wp_send_json_error(array('message' => 'Błąd kodowania JSON: ' . json_last_error_msg()));
                return;
            }

            wp_send_json_success(array('message' => 'Połączenie z bazą danych działa poprawnie. Test kodowania UTF-8 przeszedł pomyślnie.'));

        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'Błąd: ' . $e->getMessage()));
        }
    }

    /**
     * Odtworzenie tabeli
     */
    public function recreate_table() {
        if (!wp_verify_nonce($_POST['nonce'], 'pco-debug-nonce')) {
            wp_send_json_error(array('message' => 'Błąd bezpieczeństwa'));
            return;
        }

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(array('message' => 'Brak uprawnień'));
            return;
        }

        global $wpdb;

        try {
            $table_name = $wpdb->prefix . 'pco_orders';

            // Usunięcie istniejącej tabeli
            $wpdb->query("DROP TABLE IF EXISTS $table_name");

            // Utworzenie nowej tabeli
            pco_create_database_table();

            // Sprawdzenie czy tabela została utworzona
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

            if ($table_exists) {
                wp_send_json_success(array('message' => 'Tabela została pomyślnie odtworzona.'));
            } else {
                wp_send_json_error(array('message' => 'Nie udało się utworzyć tabeli.'));
            }

        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'Błąd: ' . $e->getMessage()));
        }
    }

    /**
     * Aktualizacja struktury tabeli
     */
    public function update_table_structure() {
        if (!wp_verify_nonce($_POST['nonce'], 'pco-debug-nonce')) {
            wp_send_json_error(array('message' => 'Błąd bezpieczeństwa'));
            return;
        }

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(array('message' => 'Brak uprawnień'));
            return;
        }

        global $wpdb;

        try {
            $table_name = $wpdb->prefix . 'pco_orders';

            // Sprawdzenie obecnej struktury
            $columns = $wpdb->get_results("DESCRIBE $table_name");
            $existing_columns = array();
            foreach ($columns as $column) {
                $existing_columns[] = $column->Field;
            }

            $required_columns = array(
                'id' => 'bigint(20) NOT NULL AUTO_INCREMENT',
                'order_id' => 'varchar(20) NOT NULL',
                'product_id' => 'bigint(20) NOT NULL',
                'quantity' => 'int(11) NOT NULL',
                'options_data' => 'longtext NOT NULL',
                'total_price' => 'decimal(10,2) NOT NULL',
                'form_data' => 'longtext NOT NULL',
                'created_at' => 'datetime NOT NULL'
            );

            $changes_made = false;

            // Dodanie brakujących kolumn
            foreach ($required_columns as $column_name => $column_definition) {
                if (!in_array($column_name, $existing_columns)) {
                    $sql = "ALTER TABLE $table_name ADD COLUMN $column_name $column_definition";
                    $result = $wpdb->query($sql);
                    if ($result !== false) {
                        $changes_made = true;
                    }
                }
            }

            // Usunięcie niepotrzebnych kolumn (stare kolumny)
            $old_columns = array('order_data', 'order_date');
            foreach ($old_columns as $old_column) {
                if (in_array($old_column, $existing_columns)) {
                    $sql = "ALTER TABLE $table_name DROP COLUMN $old_column";
                    $wpdb->query($sql);
                    $changes_made = true;
                }
            }

            if ($changes_made) {
                wp_send_json_success(array('message' => 'Struktura tabeli została zaktualizowana.'));
            } else {
                wp_send_json_success(array('message' => 'Struktura tabeli jest już aktualna.'));
            }

        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'Błąd: ' . $e->getMessage()));
        }
    }
}
