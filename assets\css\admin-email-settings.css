/* Styles for Email Settings Admin Page */

.pco-email-settings {
    max-width: 1200px;
}

.pco-variables-info {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 30px;
}

.pco-variables-info h3 {
    margin-top: 0;
    color: #333;
}

.pco-variables-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.pco-variable {
    display: block;
    padding: 8px 12px;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
}

.pco-variable:hover {
    background: #e7f3ff;
    border-color: #0073aa;
}

.pco-variable code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: bold;
    color: #d63384;
}

.pco-template-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.pco-template-section h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #23282d;
}

.pco-template-section .form-table {
    margin-top: 20px;
}

.pco-template-section .form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
    vertical-align: top;
}

.pco-template-section .form-table td {
    padding: 15px 10px;
}

.pco-template-section .regular-text {
    width: 100%;
    max-width: 500px;
}

.pco-template-section .wp-editor-container {
    border: 1px solid #ddd;
    border-radius: 3px;
}

.pco-actions {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    text-align: left;
}

.pco-actions .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

/* Modal styles */
#pco-email-preview-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.pco-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    border-radius: 4px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.pco-modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    right: 15px;
    top: 10px;
    cursor: pointer;
}

.pco-modal-close:hover,
.pco-modal-close:focus {
    color: #000;
    text-decoration: none;
}

#pco-email-preview-content {
    margin-top: 20px;
    border: 1px solid #ddd;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 3px;
}

#pco-email-preview-content iframe {
    width: 100%;
    min-height: 400px;
    border: none;
    background: #fff;
}

/* Responsive design */
@media (max-width: 768px) {
    .pco-variables-list {
        grid-template-columns: 1fr;
    }
    
    .pco-modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }
    
    .pco-template-section .form-table th,
    .pco-template-section .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .pco-template-section .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
}

/* WordPress admin compatibility */
.wrap .pco-email-settings h1 {
    margin-bottom: 20px;
}

.pco-email-settings .notice {
    margin: 5px 0 15px;
}

/* Form styling improvements */
.pco-template-section input[type="text"],
.pco-template-section input[type="email"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
}

.pco-template-section input[type="text"]:focus,
.pco-template-section input[type="email"]:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.pco-template-section input[type="checkbox"] {
    margin-right: 8px;
}

.pco-template-section .description {
    color: #666;
    font-style: italic;
    margin-top: 5px;
}

/* Loading state */
.pco-loading {
    opacity: 0.6;
    pointer-events: none;
}

.pco-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: pco-spin 1s linear infinite;
}

@keyframes pco-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
