<?php
/**
 * <PERSON><PERSON>a renderująca formularze na podstawie konfiguracji
 */
class PCO_Form_Renderer {
    
    private $form_builder;
    
    /**
     * Konstruktor
     */
    public function __construct() {
        $this->form_builder = new PCO_Form_Builder();
    }
    
    /**
     * Renderowanie formularza na podstawie konfiguracji
     */
    public function render_form($include_wrapper = true) {
        $config = $this->form_builder->get_form_config();
        
        if (empty($config['sections'])) {
            return '<p>' . __('Brak konfiguracji formularza.', 'papierotka-custom-order') . '</p>';
        }
        
        $html = '';
        
        if ($include_wrapper) {
            $html .= '<form id="pco-order-form" class="pco-order-form">';
            $html .= $this->render_hidden_fields();
        }
        
        foreach ($config['sections'] as $section) {
            $html .= $this->render_section($section);
        }
        
        if ($include_wrapper) {
            $html .= $this->render_form_actions();
            $html .= wp_nonce_field('pco-form-submission', 'pco_nonce', true, false);
            $html .= '</form>';
        }
        
        return $html;
    }
    
    /**
     * Renderowanie formularza do podglądu
     */
    public function render_form_preview($config) {
        if (empty($config['sections'])) {
            return '<p>' . __('Brak sekcji w formularzu.', 'papierotka-custom-order') . '</p>';
        }
        
        $html = '<div class="pco-form-preview">';
        $html .= '<form class="pco-order-form">';
        
        foreach ($config['sections'] as $section) {
            $html .= $this->render_section($section);
        }
        
        $html .= '<div class="pco-form-actions">';
        $html .= '<button type="button" class="pco-submit-button" disabled>' . __('Złóż zamówienie', 'papierotka-custom-order') . '</button>';
        $html .= '</div>';
        $html .= '</form>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Renderowanie ukrytych pól z danymi produktu
     */
    private function render_hidden_fields() {
        $product_id = WC()->session->get('pco_product_id');
        $quantity = WC()->session->get('pco_quantity');
        $options_data = WC()->session->get('pco_options_data');
        $total_price = WC()->session->get('pco_total_price');
        
        $html = '';
        $html .= '<input type="hidden" name="pco_product_id" value="' . esc_attr($product_id) . '">';
        $html .= '<input type="hidden" name="pco_quantity" value="' . esc_attr($quantity) . '">';
        $html .= '<input type="hidden" name="pco_options_data" value=\'' . esc_attr(json_encode($options_data)) . '\'>';
        $html .= '<input type="hidden" name="pco_total_price" value="' . esc_attr($total_price) . '">';
        
        return $html;
    }
    
    /**
     * Renderowanie sekcji formularza
     */
    private function render_section($section) {
        $html = '<div class="pco-form-section">';
        
        if (!empty($section['title'])) {
            $html .= '<h2>' . esc_html($section['title']) . '</h2>';
        }
        
        if (!empty($section['description'])) {
            $html .= '<p class="pco-section-description">' . esc_html($section['description']) . '</p>';
        }
        
        if (isset($section['fields']) && is_array($section['fields'])) {
            foreach ($section['fields'] as $field) {
                $html .= $this->render_field($field);
            }
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Renderowanie pojedynczego pola
     */
    private function render_field($field) {
        if (empty($field['type']) || empty($field['id'])) {
            return '';
        }
        
        $field_id = 'pco-' . $field['id'];
        $field_name = $field['id'];
        $field_label = isset($field['label']) ? $field['label'] : '';
        $field_placeholder = isset($field['placeholder']) ? $field['placeholder'] : '';
        $field_required = isset($field['required']) && $field['required'];
        $field_css_class = isset($field['css_class']) ? $field['css_class'] : '';
        $field_help_text = isset($field['help_text']) ? $field['help_text'] : '';
        
        $html = '<div class="pco-form-field ' . esc_attr($field_css_class) . '">';
        
        // Etykieta pola
        if (!empty($field_label)) {
            $required_mark = $field_required ? ' *' : '';
            $html .= '<label for="' . esc_attr($field_id) . '">' . esc_html($field_label . $required_mark) . '</label>';
        }
        
        // Pole formularza
        switch ($field['type']) {
            case 'text':
            case 'email':
            case 'tel':
            case 'date':
            case 'time':
                $html .= $this->render_input_field($field, $field_id, $field_name, $field_placeholder, $field_required);
                break;
                
            case 'textarea':
                $html .= $this->render_textarea_field($field, $field_id, $field_name, $field_placeholder, $field_required);
                break;
                
            case 'select':
                $html .= $this->render_select_field($field, $field_id, $field_name, $field_required);
                break;
                
            case 'checkbox':
                $html .= $this->render_checkbox_field($field, $field_id, $field_name, $field_required);
                break;
                
            case 'radio':
                $html .= $this->render_radio_field($field, $field_id, $field_name, $field_required);
                break;
                
            case 'file':
                $html .= $this->render_file_field($field, $field_id, $field_name, $field_required);
                break;
        }
        
        // Tekst pomocy
        if (!empty($field_help_text)) {
            $html .= '<p class="pco-field-help">' . esc_html($field_help_text) . '</p>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Renderowanie pola input
     */
    private function render_input_field($field, $field_id, $field_name, $field_placeholder, $field_required) {
        $attributes = array(
            'type' => $field['type'],
            'id' => $field_id,
            'name' => $field_name,
            'placeholder' => $field_placeholder
        );
        
        if ($field_required) {
            $attributes['required'] = 'required';
        }
        
        $html = '<input';
        foreach ($attributes as $attr => $value) {
            if (!empty($value) || $attr === 'required') {
                $html .= ' ' . $attr . '="' . esc_attr($value) . '"';
            }
        }
        $html .= '>';
        
        return $html;
    }
    
    /**
     * Renderowanie pola textarea
     */
    private function render_textarea_field($field, $field_id, $field_name, $field_placeholder, $field_required) {
        $rows = isset($field['rows']) ? intval($field['rows']) : 5;
        
        $attributes = array(
            'id' => $field_id,
            'name' => $field_name,
            'rows' => $rows,
            'placeholder' => $field_placeholder
        );
        
        if ($field_required) {
            $attributes['required'] = 'required';
        }
        
        $html = '<textarea';
        foreach ($attributes as $attr => $value) {
            if (!empty($value) || $attr === 'required') {
                $html .= ' ' . $attr . '="' . esc_attr($value) . '"';
            }
        }
        $html .= '></textarea>';
        
        return $html;
    }
    
    /**
     * Renderowanie pola select
     */
    private function render_select_field($field, $field_id, $field_name, $field_required) {
        $options = isset($field['options']) ? $field['options'] : array();
        
        $html = '<select id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '"';
        if ($field_required) {
            $html .= ' required="required"';
        }
        $html .= '>';
        
        $html .= '<option value="">' . __('Wybierz opcję...', 'papierotka-custom-order') . '</option>';
        
        foreach ($options as $option) {
            $value = isset($option['value']) ? $option['value'] : '';
            $label = isset($option['label']) ? $option['label'] : $value;
            $html .= '<option value="' . esc_attr($value) . '">' . esc_html($label) . '</option>';
        }
        
        $html .= '</select>';
        
        return $html;
    }
    
    /**
     * Renderowanie pola checkbox
     */
    private function render_checkbox_field($field, $field_id, $field_name, $field_required) {
        $html = '<input type="checkbox" id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '" value="1"';
        if ($field_required) {
            $html .= ' required="required"';
        }
        $html .= '>';
        
        return $html;
    }
    
    /**
     * Renderowanie pola radio
     */
    private function render_radio_field($field, $field_id, $field_name, $field_required) {
        $options = isset($field['options']) ? $field['options'] : array();
        $html = '';
        
        foreach ($options as $index => $option) {
            $value = isset($option['value']) ? $option['value'] : '';
            $label = isset($option['label']) ? $option['label'] : $value;
            $option_id = $field_id . '_' . $index;
            
            $html .= '<div class="pco-radio-option">';
            $html .= '<input type="radio" id="' . esc_attr($option_id) . '" name="' . esc_attr($field_name) . '" value="' . esc_attr($value) . '"';
            if ($field_required) {
                $html .= ' required="required"';
            }
            $html .= '>';
            $html .= '<label for="' . esc_attr($option_id) . '">' . esc_html($label) . '</label>';
            $html .= '</div>';
        }
        
        return $html;
    }
    
    /**
     * Renderowanie pola file
     */
    private function render_file_field($field, $field_id, $field_name, $field_required) {
        $html = '<input type="file" id="' . esc_attr($field_id) . '" name="' . esc_attr($field_name) . '"';
        
        if ($field_required) {
            $html .= ' required="required"';
        }
        
        // Dodanie ograniczeń typów plików
        if (isset($field['validation']['file_types']) && is_array($field['validation']['file_types'])) {
            $accept = array();
            foreach ($field['validation']['file_types'] as $type) {
                $accept[] = '.' . $type;
            }
            $html .= ' accept="' . esc_attr(implode(',', $accept)) . '"';
        }
        
        $html .= '>';
        
        return $html;
    }
    
    /**
     * Renderowanie przycisków akcji formularza
     */
    private function render_form_actions() {
        $html = '<div class="pco-form-actions">';
        $html .= '<button type="submit" class="pco-submit-button">' . __('Złóż zamówienie', 'papierotka-custom-order') . '</button>';
        $html .= '</div>';
        
        return $html;
    }
}
