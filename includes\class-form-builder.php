<?php
/**
 * Klasa zarządzająca konstruktorem formularzy
 */
class PCO_Form_Builder {

    /**
     * Konstruktor
     */
    public function __construct() {
        // Dodanie menu w panelu administracyjnym
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Obsługa zapisywania ustawień formularza
        add_action('admin_post_pco_save_form_builder', array($this, 'save_form_builder'));

        // Dodanie skryptów i stylów dla panelu administracyjnego
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Obsługa AJAX
        add_action('wp_ajax_pco_save_form_config', array($this, 'ajax_save_form_config'));
        add_action('wp_ajax_pco_preview_form', array($this, 'ajax_preview_form'));

        // Inicjalizacja domyślnej konfiguracji formularza
        add_action('init', array($this, 'init_default_form_config'));
    }

    /**
     * Dodanie menu w panelu administracyjnym
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Konstruktor Formularza - Papierotka', 'papierotka-custom-order'),
            __('Konstruktor Formularza', 'papierotka-custom-order'),
            'manage_woocommerce',
            'pco-form-builder',
            array($this, 'admin_page')
        );
    }

    /**
     * Inicjalizacja domyślnej konfiguracji formularza
     */
    public function init_default_form_config() {
        // Sprawdzenie czy konfiguracja już istnieje
        if (get_option('pco_form_config_initialized')) {
            return;
        }

        // Domyślna konfiguracja formularza
        $default_config = array(
            'sections' => array(
                array(
                    'id' => 'contact_data',
                    'title' => 'Dane kontaktowe',
                    'description' => '',
                    'fields' => array(
                        array(
                            'id' => 'name',
                            'type' => 'text',
                            'label' => 'Imię i nazwisko',
                            'placeholder' => '',
                            'required' => true,
                            'validation' => array('min_length' => 2),
                            'css_class' => '',
                            'help_text' => ''
                        ),
                        array(
                            'id' => 'email',
                            'type' => 'email',
                            'label' => 'Adres e-mail',
                            'placeholder' => '',
                            'required' => true,
                            'validation' => array('email' => true),
                            'css_class' => '',
                            'help_text' => ''
                        ),
                        array(
                            'id' => 'phone',
                            'type' => 'tel',
                            'label' => 'Telefon',
                            'placeholder' => '',
                            'required' => true,
                            'validation' => array('min_length' => 9),
                            'css_class' => '',
                            'help_text' => ''
                        )
                    )
                ),
                array(
                    'id' => 'invitation_details',
                    'title' => 'Szczegóły zaproszenia',
                    'description' => '',
                    'fields' => array(
                        array(
                            'id' => 'bride_name',
                            'type' => 'text',
                            'label' => 'Imię Panny Młodej',
                            'placeholder' => '',
                            'required' => true,
                            'validation' => array('min_length' => 2),
                            'css_class' => '',
                            'help_text' => ''
                        ),
                        array(
                            'id' => 'groom_name',
                            'type' => 'text',
                            'label' => 'Imię Pana Młodego',
                            'placeholder' => '',
                            'required' => true,
                            'validation' => array('min_length' => 2),
                            'css_class' => '',
                            'help_text' => ''
                        ),
                        array(
                            'id' => 'wedding_date',
                            'type' => 'date',
                            'label' => 'Data ślubu',
                            'placeholder' => '',
                            'required' => true,
                            'validation' => array('date' => true),
                            'css_class' => '',
                            'help_text' => ''
                        ),
                        array(
                            'id' => 'wedding_time',
                            'type' => 'time',
                            'label' => 'Godzina ślubu',
                            'placeholder' => '',
                            'required' => true,
                            'validation' => array('time' => true),
                            'css_class' => '',
                            'help_text' => ''
                        )
                    )
                ),
                array(
                    'id' => 'venue_details',
                    'title' => 'Szczegóły lokalizacji',
                    'description' => '',
                    'fields' => array(
                        array(
                            'id' => 'church_name',
                            'type' => 'text',
                            'label' => 'Nazwa kościoła / USC',
                            'placeholder' => '',
                            'required' => false,
                            'validation' => array(),
                            'css_class' => '',
                            'help_text' => ''
                        ),
                        array(
                            'id' => 'church_address',
                            'type' => 'text',
                            'label' => 'Adres kościoła / USC',
                            'placeholder' => '',
                            'required' => false,
                            'validation' => array(),
                            'css_class' => '',
                            'help_text' => ''
                        ),
                        array(
                            'id' => 'reception_name',
                            'type' => 'text',
                            'label' => 'Nazwa lokalu weselnego',
                            'placeholder' => '',
                            'required' => true,
                            'validation' => array('min_length' => 2),
                            'css_class' => '',
                            'help_text' => ''
                        ),
                        array(
                            'id' => 'reception_address',
                            'type' => 'text',
                            'label' => 'Adres lokalu weselnego',
                            'placeholder' => '',
                            'required' => true,
                            'validation' => array('min_length' => 5),
                            'css_class' => '',
                            'help_text' => ''
                        ),
                        array(
                            'id' => 'reception_time',
                            'type' => 'time',
                            'label' => 'Godzina rozpoczęcia wesela',
                            'placeholder' => '',
                            'required' => true,
                            'validation' => array('time' => true),
                            'css_class' => '',
                            'help_text' => ''
                        )
                    )
                ),
                array(
                    'id' => 'guest_list',
                    'title' => 'Lista gości',
                    'description' => '',
                    'fields' => array(
                        array(
                            'id' => 'guest_list',
                            'type' => 'textarea',
                            'label' => 'Lista gości (opcjonalnie)',
                            'placeholder' => 'Wprowadź listę gości, każdy gość w nowej linii',
                            'required' => false,
                            'validation' => array(),
                            'css_class' => '',
                            'help_text' => '',
                            'rows' => 10
                        ),
                        array(
                            'id' => 'guest_list_file',
                            'type' => 'file',
                            'label' => 'Lub załącz plik z listą gości',
                            'placeholder' => '',
                            'required' => false,
                            'validation' => array('file_types' => array('txt', 'doc', 'docx', 'pdf')),
                            'css_class' => '',
                            'help_text' => 'Dozwolone formaty: TXT, DOC, DOCX, PDF'
                        )
                    )
                ),
                array(
                    'id' => 'additional_info',
                    'title' => 'Dodatkowe informacje',
                    'description' => '',
                    'fields' => array(
                        array(
                            'id' => 'additional_info',
                            'type' => 'textarea',
                            'label' => 'Dodatkowe informacje lub uwagi',
                            'placeholder' => '',
                            'required' => false,
                            'validation' => array(),
                            'css_class' => '',
                            'help_text' => '',
                            'rows' => 5
                        )
                    )
                )
            )
        );

        // Zapisanie domyślnej konfiguracji
        update_option('pco_form_config', $default_config);
        update_option('pco_form_config_initialized', true);
    }

    /**
     * Pobranie konfiguracji formularza
     */
    public function get_form_config() {
        return get_option('pco_form_config', array());
    }

    /**
     * Zapisanie konfiguracji formularza
     */
    public function save_form_config($config) {
        return update_option('pco_form_config', $config);
    }

    /**
     * Pobranie dostępnych typów pól
     */
    public function get_field_types() {
        return array(
            'text' => array(
                'label' => __('Tekst', 'papierotka-custom-order'),
                'icon' => 'dashicons-edit',
                'supports' => array('placeholder', 'validation', 'css_class', 'help_text')
            ),
            'email' => array(
                'label' => __('E-mail', 'papierotka-custom-order'),
                'icon' => 'dashicons-email',
                'supports' => array('placeholder', 'validation', 'css_class', 'help_text')
            ),
            'tel' => array(
                'label' => __('Telefon', 'papierotka-custom-order'),
                'icon' => 'dashicons-phone',
                'supports' => array('placeholder', 'validation', 'css_class', 'help_text')
            ),
            'date' => array(
                'label' => __('Data', 'papierotka-custom-order'),
                'icon' => 'dashicons-calendar-alt',
                'supports' => array('validation', 'css_class', 'help_text')
            ),
            'time' => array(
                'label' => __('Czas', 'papierotka-custom-order'),
                'icon' => 'dashicons-clock',
                'supports' => array('validation', 'css_class', 'help_text')
            ),
            'textarea' => array(
                'label' => __('Obszar tekstowy', 'papierotka-custom-order'),
                'icon' => 'dashicons-text',
                'supports' => array('placeholder', 'validation', 'css_class', 'help_text', 'rows')
            ),
            'select' => array(
                'label' => __('Lista rozwijana', 'papierotka-custom-order'),
                'icon' => 'dashicons-arrow-down-alt2',
                'supports' => array('options', 'validation', 'css_class', 'help_text')
            ),
            'checkbox' => array(
                'label' => __('Pole wyboru', 'papierotka-custom-order'),
                'icon' => 'dashicons-yes',
                'supports' => array('validation', 'css_class', 'help_text')
            ),
            'radio' => array(
                'label' => __('Przycisk opcji', 'papierotka-custom-order'),
                'icon' => 'dashicons-marker',
                'supports' => array('options', 'validation', 'css_class', 'help_text')
            ),
            'file' => array(
                'label' => __('Plik', 'papierotka-custom-order'),
                'icon' => 'dashicons-upload',
                'supports' => array('validation', 'css_class', 'help_text', 'file_types')
            )
        );
    }

    /**
     * Strona administracyjna konstruktora formularzy
     */
    public function admin_page() {
        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nie masz uprawnień do tej strony.', 'papierotka-custom-order'));
        }

        // Prevent multiple renders
        static $rendered = false;
        if ($rendered) {
            // Debug: Log multiple render attempts
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PCO Form Builder: Prevented duplicate render');
            }
            return;
        }
        $rendered = true;

        // Debug: Log successful render
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('PCO Form Builder: Rendering admin page');
        }

        // Pobranie aktualnej konfiguracji
        $form_config = $this->get_form_config();
        $field_types = $this->get_field_types();

        ?>
        <div class="wrap pco-form-builder-admin-wrap">
            <h1><?php _e('Konstruktor Formularza Zamówienia', 'papierotka-custom-order'); ?></h1>

            <?php
            // Wyświetlenie komunikatu o zapisaniu
            if (isset($_GET['settings-updated']) && $_GET['settings-updated'] == 'true') {
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Konfiguracja formularza została zapisana.', 'papierotka-custom-order') . '</p></div>';
            }
            ?>

            <div id="pco-form-builder-main" class="pco-form-builder-container">
                <!-- Panel narzędzi -->
                <div class="pco-form-builder-toolbar">
                    <h3><?php _e('Dostępne pola', 'papierotka-custom-order'); ?></h3>
                    <div class="pco-field-types">
                        <?php foreach ($field_types as $type => $config): ?>
                            <div class="pco-field-type" data-type="<?php echo esc_attr($type); ?>">
                                <span class="dashicons <?php echo esc_attr($config['icon']); ?>"></span>
                                <span class="label"><?php echo esc_html($config['label']); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="pco-form-actions">
                        <button type="button" class="button button-primary" id="pco-save-form"><?php _e('Zapisz formularz', 'papierotka-custom-order'); ?></button>
                        <button type="button" class="button" id="pco-preview-form"><?php _e('Podgląd formularza', 'papierotka-custom-order'); ?></button>
                        <button type="button" class="button" id="pco-add-section"><?php _e('Dodaj sekcję', 'papierotka-custom-order'); ?></button>
                    </div>
                </div>

                <!-- Obszar konstruktora -->
                <div class="pco-form-builder-canvas">
                    <div id="pco-form-sections">
                        <!-- Sekcje formularza będą ładowane przez JavaScript -->
                    </div>
                </div>

                <!-- Panel właściwości -->
                <div class="pco-form-builder-properties">
                    <h3><?php _e('Właściwości', 'papierotka-custom-order'); ?></h3>
                    <div id="pco-field-properties">
                        <p><?php _e('Wybierz pole, aby edytować jego właściwości.', 'papierotka-custom-order'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal do podglądu formularza -->
        <div id="pco-form-preview-modal" style="display: none;">
            <div class="pco-modal-content">
                <span class="pco-modal-close">&times;</span>
                <h2><?php _e('Podgląd formularza', 'papierotka-custom-order'); ?></h2>
                <div id="pco-form-preview-content"></div>
            </div>
        </div>

        <script type="text/javascript">
            // Prevent multiple initializations
            if (typeof window.pcoFormConfig === 'undefined') {
                window.pcoFormConfig = <?php echo json_encode($form_config); ?>;
                window.pcoFieldTypes = <?php echo json_encode($field_types); ?>;
            }
        </script>
        <?php
    }

    /**
     * AJAX handler dla zapisywania konfiguracji formularza
     */
    public function ajax_save_form_config() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pco-admin-nonce')) {
            wp_die('Błąd bezpieczeństwa');
        }

        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Brak uprawnień');
        }

        $form_config = json_decode(stripslashes($_POST['form_config']), true);

        if ($this->save_form_config($form_config)) {
            wp_send_json_success(array(
                'message' => __('Konfiguracja formularza została zapisana.', 'papierotka-custom-order')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Błąd podczas zapisywania konfiguracji.', 'papierotka-custom-order')
            ));
        }
    }

    /**
     * AJAX handler dla podglądu formularza
     */
    public function ajax_preview_form() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pco-admin-nonce')) {
            wp_die('Błąd bezpieczeństwa');
        }

        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Brak uprawnień');
        }

        $form_config = json_decode(stripslashes($_POST['form_config']), true);

        // Generowanie HTML formularza
        $form_renderer = new PCO_Form_Renderer();
        $form_html = $form_renderer->render_form_preview($form_config);

        wp_send_json_success(array(
            'html' => $form_html
        ));
    }

    /**
     * Dodanie skryptów i stylów dla panelu administracyjnego
     */
    public function enqueue_admin_scripts($hook) {
        // Ładowanie tylko na stronie konstruktora formularzy
        if ($hook !== 'woocommerce_page_pco-form-builder') {
            return;
        }

        // Prevent multiple enqueues
        static $enqueued = false;
        if ($enqueued) {
            return;
        }
        $enqueued = true;

        // Dodanie Sortable UI
        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_script('jquery-ui-draggable');
        wp_enqueue_script('jquery-ui-droppable');

        wp_enqueue_style('pco-admin-form-builder', PCO_PLUGIN_URL . 'assets/css/admin-form-builder.css', array(), PCO_VERSION);
        wp_enqueue_script('pco-admin-form-builder', PCO_PLUGIN_URL . 'assets/js/admin-form-builder.js', array('jquery', 'jquery-ui-sortable', 'jquery-ui-draggable', 'jquery-ui-droppable'), PCO_VERSION, true);

        // Przekazanie danych do skryptu
        wp_localize_script('pco-admin-form-builder', 'pco_form_builder_data', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('pco-admin-nonce'),
            'strings' => array(
                'confirm_delete_field' => __('Czy na pewno chcesz usunąć to pole?', 'papierotka-custom-order'),
                'confirm_delete_section' => __('Czy na pewno chcesz usunąć tę sekcję?', 'papierotka-custom-order'),
                'field_label' => __('Etykieta pola', 'papierotka-custom-order'),
                'field_placeholder' => __('Tekst zastępczy', 'papierotka-custom-order'),
                'field_required' => __('Pole wymagane', 'papierotka-custom-order'),
                'field_css_class' => __('Klasa CSS', 'papierotka-custom-order'),
                'field_help_text' => __('Tekst pomocy', 'papierotka-custom-order'),
                'section_title' => __('Tytuł sekcji', 'papierotka-custom-order'),
                'section_description' => __('Opis sekcji', 'papierotka-custom-order'),
                'saving' => __('Zapisywanie...', 'papierotka-custom-order'),
                'saved' => __('Zapisano!', 'papierotka-custom-order'),
                'error' => __('Błąd!', 'papierotka-custom-order')
            )
        ));
    }

    /**
     * Walidacja konfiguracji formularza
     */
    public function validate_form_config($config) {
        $errors = array();

        if (!isset($config['sections']) || !is_array($config['sections'])) {
            $errors[] = __('Brak sekcji formularza.', 'papierotka-custom-order');
            return $errors;
        }

        foreach ($config['sections'] as $section_index => $section) {
            if (empty($section['title'])) {
                $errors[] = sprintf(__('Sekcja %d nie ma tytułu.', 'papierotka-custom-order'), $section_index + 1);
            }

            if (!isset($section['fields']) || !is_array($section['fields'])) {
                $errors[] = sprintf(__('Sekcja "%s" nie ma pól.', 'papierotka-custom-order'), $section['title']);
                continue;
            }

            foreach ($section['fields'] as $field_index => $field) {
                if (empty($field['id'])) {
                    $errors[] = sprintf(__('Pole %d w sekcji "%s" nie ma identyfikatora.', 'papierotka-custom-order'), $field_index + 1, $section['title']);
                }

                if (empty($field['type'])) {
                    $errors[] = sprintf(__('Pole "%s" nie ma typu.', 'papierotka-custom-order'), $field['id']);
                }

                if (empty($field['label'])) {
                    $errors[] = sprintf(__('Pole "%s" nie ma etykiety.', 'papierotka-custom-order'), $field['id']);
                }
            }
        }

        return $errors;
    }

    /**
     * Pobranie listy wymaganych pól na podstawie konfiguracji
     */
    public function get_required_fields() {
        $config = $this->get_form_config();
        $required_fields = array();

        if (isset($config['sections'])) {
            foreach ($config['sections'] as $section) {
                if (isset($section['fields'])) {
                    foreach ($section['fields'] as $field) {
                        if (isset($field['required']) && $field['required'] && !empty($field['id'])) {
                            $required_fields[] = $field['id'];
                        }
                    }
                }
            }
        }

        return $required_fields;
    }

    /**
     * Walidacja danych formularza na podstawie konfiguracji
     */
    public function validate_form_data($form_data) {
        $config = $this->get_form_config();
        $errors = array();

        if (!isset($config['sections'])) {
            return $errors;
        }

        foreach ($config['sections'] as $section) {
            if (!isset($section['fields'])) {
                continue;
            }

            foreach ($section['fields'] as $field) {
                $field_id = $field['id'];
                $field_value = isset($form_data[$field_id]) ? $form_data[$field_id] : '';

                // Sprawdzenie pól wymaganych
                if (isset($field['required']) && $field['required'] && empty($field_value)) {
                    $errors[] = sprintf(__('Pole "%s" jest wymagane.', 'papierotka-custom-order'), $field['label']);
                    continue;
                }

                // Walidacja na podstawie typu pola
                if (!empty($field_value)) {
                    switch ($field['type']) {
                        case 'email':
                            if (!is_email($field_value)) {
                                $errors[] = sprintf(__('Pole "%s" musi zawierać prawidłowy adres e-mail.', 'papierotka-custom-order'), $field['label']);
                            }
                            break;

                        case 'date':
                            if (!$this->validate_date($field_value)) {
                                $errors[] = sprintf(__('Pole "%s" musi zawierać prawidłową datę.', 'papierotka-custom-order'), $field['label']);
                            }
                            break;

                        case 'time':
                            if (!$this->validate_time($field_value)) {
                                $errors[] = sprintf(__('Pole "%s" musi zawierać prawidłowy czas.', 'papierotka-custom-order'), $field['label']);
                            }
                            break;
                    }

                    // Dodatkowa walidacja na podstawie reguł
                    if (isset($field['validation'])) {
                        $validation_errors = $this->validate_field_rules($field_value, $field['validation'], $field['label']);
                        $errors = array_merge($errors, $validation_errors);
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * Walidacja reguł pola
     */
    private function validate_field_rules($value, $rules, $field_label) {
        $errors = array();

        if (isset($rules['min_length']) && strlen($value) < $rules['min_length']) {
            $errors[] = sprintf(__('Pole "%s" musi mieć co najmniej %d znaków.', 'papierotka-custom-order'), $field_label, $rules['min_length']);
        }

        if (isset($rules['max_length']) && strlen($value) > $rules['max_length']) {
            $errors[] = sprintf(__('Pole "%s" może mieć maksymalnie %d znaków.', 'papierotka-custom-order'), $field_label, $rules['max_length']);
        }

        return $errors;
    }

    /**
     * Walidacja daty
     */
    private function validate_date($date) {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * Walidacja czasu
     */
    private function validate_time($time) {
        $t = DateTime::createFromFormat('H:i', $time);
        return $t && $t->format('H:i') === $time;
    }
}
