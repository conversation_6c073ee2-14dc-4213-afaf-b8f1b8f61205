<?php
/**
 * Klasa zarządzająca szablonami e-mail
 */
class PCO_Email_Templates {

    /**
     * Konstruktor
     */
    public function __construct() {
        // Inicjalizacja domyślnych szablonów przy aktywacji wtyczki
        add_action('init', array($this, 'init_default_templates'));
    }

    /**
     * Inicjalizacja domyślnych szablonów e-mail
     */
    public function init_default_templates() {
        // Sprawdzenie czy szablony już istnieją
        if (get_option('pco_email_templates_initialized')) {
            return;
        }

        // Domyślne szablony
        $default_templates = array(
            'admin_notification' => array(
                'subject' => 'Nowe zamówienie #{order_id}',
                'content' => $this->get_default_admin_template(),
                'enabled' => true
            ),
            'customer_confirmation' => array(
                'subject' => 'Potwierdzenie zamówienia #{order_id}',
                'content' => $this->get_default_customer_template(),
                'enabled' => true
            )
        );

        // Zapisanie domyślnych szablonów
        update_option('pco_email_templates', $default_templates);
        update_option('pco_email_templates_initialized', true);
    }

    /**
     * Pobranie szablonu e-mail
     */
    public function get_template($template_type) {
        $templates = get_option('pco_email_templates', array());

        if (isset($templates[$template_type])) {
            return $templates[$template_type];
        }

        return false;
    }

    /**
     * Zapisanie szablonu e-mail
     */
    public function save_template($template_type, $subject, $content, $enabled = true) {
        $templates = get_option('pco_email_templates', array());

        $templates[$template_type] = array(
            'subject' => sanitize_text_field($subject),
            'content' => wp_kses_post($content),
            'enabled' => (bool) $enabled
        );

        return update_option('pco_email_templates', $templates);
    }

    /**
     * Przetworzenie szablonu z zmiennymi
     */
    public function process_template($template_content, $variables = array()) {
        $processed_content = $template_content;

        // Zastąpienie zmiennych
        foreach ($variables as $key => $value) {
            $processed_content = str_replace('{' . $key . '}', $value, $processed_content);
        }

        return $processed_content;
    }

    /**
     * Pobranie dostępnych zmiennych dla szablonów
     */
    public function get_available_variables() {
        return array(
            '{order_id}' => __('ID zamówienia', 'papierotka-custom-order'),
            '{customer_name}' => __('Imię i nazwisko klienta', 'papierotka-custom-order'),
            '{customer_email}' => __('E-mail klienta', 'papierotka-custom-order'),
            '{customer_phone}' => __('Telefon klienta', 'papierotka-custom-order'),
            '{bride_name}' => __('Imię Panny Młodej', 'papierotka-custom-order'),
            '{groom_name}' => __('Imię Pana Młodego', 'papierotka-custom-order'),
            '{wedding_date}' => __('Data ślubu', 'papierotka-custom-order'),
            '{wedding_time}' => __('Godzina ślubu', 'papierotka-custom-order'),
            '{product_name}' => __('Nazwa produktu', 'papierotka-custom-order'),
            '{quantity}' => __('Ilość', 'papierotka-custom-order'),
            '{total_price}' => __('Cena łączna', 'papierotka-custom-order'),
            '{order_details}' => __('Szczegóły zamówienia', 'papierotka-custom-order'),
            '{site_name}' => __('Nazwa strony', 'papierotka-custom-order'),
            '{site_url}' => __('Adres strony', 'papierotka-custom-order')
        );
    }

    /**
     * Domyślny szablon dla administratora
     */
    private function get_default_admin_template() {
        return '<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>
<body>
    <h2>Nowe zamówienie #{order_id}</h2>

    <h3>Dane klienta:</h3>
    <ul>
        <li><strong>Imię i nazwisko:</strong> {customer_name}</li>
        <li><strong>E-mail:</strong> {customer_email}</li>
        <li><strong>Telefon:</strong> {customer_phone}</li>
        <li><strong>Imię Panny Młodej:</strong> {bride_name}</li>
        <li><strong>Imię Pana Młodego:</strong> {groom_name}</li>
        <li><strong>Data ślubu:</strong> {wedding_date}</li>
        <li><strong>Godzina ślubu:</strong> {wedding_time}</li>
    </ul>

    <h3>Szczegóły zamówienia:</h3>
    <p><strong>Produkt:</strong> {product_name}</p>
    <p><strong>Ilość:</strong> {quantity}</p>
    <p><strong>Cena łączna:</strong> {total_price} zł</p>

    {order_details}

    <hr>
    <p>To powiadomienie zostało wysłane automatycznie z {site_name} ({site_url})</p>
</body>
</html>';
    }

    /**
     * Domyślny szablon dla klienta
     */
    private function get_default_customer_template() {
        return '<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>
<body>
    <h2>Dziękujemy za złożenie zamówienia #{order_id}</h2>

    <p>Szanowni Państwo {customer_name},</p>

    <p>Dziękujemy za złożenie zamówienia w naszym sklepie. Twoje zamówienie zostało przyjęte do realizacji.</p>

    <h3>Szczegóły zamówienia:</h3>
    <ul>
        <li><strong>Numer zamówienia:</strong> {order_id}</li>
        <li><strong>Produkt:</strong> {product_name}</li>
        <li><strong>Ilość:</strong> {quantity}</li>
        <li><strong>Cena łączna:</strong> {total_price} zł</li>
    </ul>

    <p>Skontaktujemy się z Państwem wkrótce w celu potwierdzenia szczegółów zamówienia.</p>

    <p>W razie pytań prosimy o kontakt.</p>

    <p>Pozdrawiamy,<br>
    Zespół {site_name}</p>

    <hr>
    <p>To wiadomość została wysłana automatycznie z {site_name} ({site_url})</p>
</body>
</html>';
    }

    /**
     * Generowanie zmiennych dla szablonu na podstawie danych zamówienia
     */
    public function generate_template_variables($order_id, $form_data) {
        // Pobranie danych produktu
        $product_id = isset($form_data['pco_product_id']) ? intval($form_data['pco_product_id']) : 0;
        $product = wc_get_product($product_id);
        $product_name = $product ? $product->get_name() : __('Produkt', 'papierotka-custom-order');

        // Obliczenie ceny
        $total_price = isset($form_data['pco_total_price']) ? floatval($form_data['pco_total_price']) : 0;
        $quantity = isset($form_data['pco_quantity']) ? intval($form_data['pco_quantity']) : 1;

        // Generowanie szczegółów zamówienia
        $order_details = $this->generate_order_details($form_data);

        return array(
            'order_id' => $order_id,
            'customer_name' => isset($form_data['name']) ? $form_data['name'] : '',
            'customer_email' => isset($form_data['email']) ? $form_data['email'] : '',
            'customer_phone' => isset($form_data['phone']) ? $form_data['phone'] : '',
            'bride_name' => isset($form_data['bride_name']) ? $form_data['bride_name'] : '',
            'groom_name' => isset($form_data['groom_name']) ? $form_data['groom_name'] : '',
            'wedding_date' => isset($form_data['wedding_date']) ? $form_data['wedding_date'] : '',
            'wedding_time' => isset($form_data['wedding_time']) ? $form_data['wedding_time'] : '',
            'product_name' => $product_name,
            'quantity' => $quantity,
            'total_price' => number_format($total_price * $quantity, 2),
            'order_details' => $order_details,
            'site_name' => get_bloginfo('name'),
            'site_url' => home_url()
        );
    }

    /**
     * Generowanie szczegółów zamówienia
     */
    private function generate_order_details($form_data) {
        $details = '<h3>' . __('Szczegółowe dane zamówienia:', 'papierotka-custom-order') . '</h3>';

        // Dodanie wybranych opcji produktu
        $details .= $this->generate_selected_options($form_data);

        // Mapowanie kluczy na polskie etykiety
        $field_labels = array(
            'church_name' => 'Nazwa kościoła / USC',
            'church_address' => 'Adres kościoła / USC',
            'reception_name' => 'Nazwa lokalu weselnego',
            'reception_address' => 'Adres lokalu weselnego',
            'reception_time' => 'Godzina przyjęcia',
            'guest_list' => 'Lista gości',
            'additional_info' => 'Dodatkowe informacje'
        );

        $details .= '<h4>' . __('Dane ślubu i wesela:', 'papierotka-custom-order') . '</h4>';
        $details .= '<ul>';
        foreach ($field_labels as $key => $label) {
            if (isset($form_data[$key]) && !empty($form_data[$key])) {
                $value = $form_data[$key];
                if ($key === 'guest_list') {
                    $value = nl2br(esc_html($value));
                }
                $details .= '<li><strong>' . $label . ':</strong> ' . $value . '</li>';
            }
        }
        $details .= '</ul>';

        return $details;
    }

    /**
     * Generowanie listy wybranych opcji
     */
    private function generate_selected_options($form_data) {
        $options_html = '';

        // Sprawdzenie czy są dostępne dane opcji
        if (!isset($form_data['pco_options_data'])) {
            return $options_html;
        }

        $options_data = array();

        // Dekodowanie danych opcji
        if (is_array($form_data['pco_options_data'])) {
            $options_data = $form_data['pco_options_data'];
        } else {
            $json_data = stripslashes($form_data['pco_options_data']);
            $options_data = json_decode($json_data, true);
        }

        if (empty($options_data)) {
            return $options_html;
        }

        $options_html .= '<h4>' . __('Wybrane opcje:', 'papierotka-custom-order') . '</h4>';
        $options_html .= '<ul>';

        foreach ($options_data as $category => $options) {
            $category_name = $this->get_category_name($category);
            $options_html .= '<li><strong>' . $category_name . ':</strong> ';

            if (is_array($options)) {
                // Dla opcji wielokrotnego wyboru
                $option_names = array_map(function($option) {
                    if (is_array($option) && isset($option['name'])) {
                        return $option['name'];
                    }
                    return $option;
                }, $options);
                $options_html .= implode(', ', $option_names);
            } else {
                // Dla opcji pojedynczego wyboru
                if (preg_match('/u[0-9a-f]{4}/i', $options)) {
                    $decoded_option = json_decode('"' . $options . '"');
                    if ($decoded_option) {
                        $options_html .= $decoded_option;
                    } else {
                        $options_html .= $options;
                    }
                } else {
                    $options_html .= $options;
                }
            }

            $options_html .= '</li>';
        }

        $options_html .= '</ul>';

        return $options_html;
    }

    /**
     * Pobieranie nazwy kategorii
     */
    private function get_category_name($category_id) {
        $categories = array(
            'koperta' => __('Koperta', 'papierotka-custom-order'),
            'personalizacja_koperty' => __('Personalizacja koperty', 'papierotka-custom-order'),
            'lak' => __('Lak', 'papierotka-custom-order'),
            'karty_zaproszenia' => __('Karty zaproszenia', 'papierotka-custom-order'),
            'zlocenia' => __('Złocenia', 'papierotka-custom-order'),
            'wstazka_sznurek' => __('Wstążka / Sznurek', 'papierotka-custom-order'),
            'opaska' => __('Opaska', 'papierotka-custom-order'),
            'dodatki' => __('Dodatki', 'papierotka-custom-order')
        );

        return isset($categories[$category_id]) ? $categories[$category_id] : $category_id;
    }
}
