<?php
/**
 * Szablon strony potwierdzenia zamówienia
 */

// Zabezpieczenie przed bezpośrednim dostępem
if (!defined('ABSPATH')) {
    exit;
}

// Pobranie ID zamówienia
$order_id = isset($_GET['order_id']) ? sanitize_text_field($_GET['order_id']) : '';

// Ustawienie tytułu strony
$pco_page_title = __('Potwierdzenie zamówienia', 'papierotka-custom-order');
add_filter('the_title', function($title) use ($pco_page_title) {
    if (in_the_loop() && is_main_query()) {
        return $pco_page_title;
    }
    return $title;
});

// Bezpośrednia manipulacja nagłówkiem strony
?>
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        // Zmiana tytułu strony w przeglądarce
        document.title = '<?php echo esc_js($pco_page_title . ' | ' . get_bloginfo('name')); ?>';
        
        // Sprawdzenie, czy jesteśmy na stronie potwierdzenia zamówienia
        // Sprawdzamy URL, aby upewnić się, że nie jesteśmy na prawdziwej stronie bloga
        if (window.location.href.indexOf('potwierdzenie-zamowienia') > -1) {
            // Próba zmiany tytułu w breadcrumbs i innych elementach
            var blogTitles = document.querySelectorAll('.entry-title, .page-title, .breadcrumb li:last-child, .breadcrumbs li:last-child, .breadcrumb_last, .breadcrumb-item.active, .current-item');
            blogTitles.forEach(function(element) {
                if (element.textContent.trim() === 'Blog') {
                    element.textContent = '<?php echo esc_js($pco_page_title); ?>';
                }
            });
            
            // Specyficzne celowanie w linki breadcrumbs
            var breadcrumbLinks = document.querySelectorAll('.breadcrumb a, .breadcrumbs a, .breadcrumb-item a, nav.woocommerce-breadcrumb a');
            breadcrumbLinks.forEach(function(link) {
                if (link.textContent.trim() === 'Blog') {
                    link.textContent = '<?php echo esc_js($pco_page_title); ?>';
                }
            });
            
            // Próba znalezienia i zmiany tekstu "Blog" w dowolnym elemencie, ale tylko w breadcrumbs
            var breadcrumbContainers = document.querySelectorAll('.breadcrumb, .breadcrumbs, .woocommerce-breadcrumb, .breadcrumb-trail, .rank-math-breadcrumb, .yoast-breadcrumb');
            breadcrumbContainers.forEach(function(container) {
                var elements = container.querySelectorAll('*');
                elements.forEach(function(element) {
                    if (element.childNodes && element.childNodes.length === 1 && element.childNodes[0].nodeType === 3) {
                        // Element zawiera tylko tekst
                        if (element.textContent.trim() === 'Blog') {
                            element.textContent = '<?php echo esc_js($pco_page_title); ?>';
                        }
                    }
                });
            });
        }
    });
</script>
<?php

get_header();
?>

<div class="pco-confirmation-container">
    <div class="pco-confirmation-wrapper">
        <h1><?php _e('Zamówienie przyjęte', 'papierotka-custom-order'); ?></h1>
        
        <div class="pco-confirmation-message">
            <?php if ($order_id) : ?>
                <p><?php printf(__('Dziękujemy! Twoje zamówienie #%s zostało przyjęte do realizacji.', 'papierotka-custom-order'), $order_id); ?></p>
                <p><?php _e('Potwierdzenie zamówienia zostało wysłane na podany adres e-mail.', 'papierotka-custom-order'); ?></p>
                <p><?php _e('Skontaktujemy się z Tobą wkrótce w celu potwierdzenia szczegółów zamówienia.', 'papierotka-custom-order'); ?></p>
            <?php else : ?>
                <p><?php _e('Dziękujemy! Twoje zamówienie zostało przyjęte do realizacji.', 'papierotka-custom-order'); ?></p>
                <p><?php _e('Potwierdzenie zamówienia zostało wysłane na podany adres e-mail.', 'papierotka-custom-order'); ?></p>
                <p><?php _e('Skontaktujemy się z Tobą wkrótce w celu potwierdzenia szczegółów zamówienia.', 'papierotka-custom-order'); ?></p>
            <?php endif; ?>
        </div>
        
        <div class="pco-confirmation-actions">
            <a href="<?php echo esc_url(home_url()); ?>" class="pap-btn-primary"><?php _e('Powrót do strony głównej', 'papierotka-custom-order'); ?></a>
        </div>
    </div>
</div>

<?php
get_footer();