<?php
/**
 * Szablon strony formularza zamówienia
 */

// Zabezpieczenie przed bezpośrednim dostępem
if (!defined('ABSPATH')) {
    exit;
}

// Włączenie debugowania dla tego pliku
define('PCO_DEBUG', true);

// Sprawdzenie czy sesja WooCommerce jest aktywna
if (!WC()->session->has_session()) {
    pco_log('Brak aktywnej sesji WooCommerce na stronie formularza - próba inicjalizacji');
    WC()->session->set_customer_session_cookie(true);
}

// Sprawdzenie czy dane zamówienia są dostępne
$product_id = WC()->session->get('pco_product_id');
pco_log('Strona formularza - product_id z sesji: ' . ($product_id ? $product_id : 'brak'));

if (!$product_id) {
    pco_log('Brak danych produktu w sesji - przekierowanie na stronę główną');
    wp_redirect(home_url());
    exit;
}

get_header();
?>

<div class="pco-order-form-container">
    <div class="pco-order-form-wrapper">
        <h1><?php _e('Formularz zamówienia', 'papierotka-custom-order'); ?></h1>

        <?php
        // Wyświetlenie podsumowania zamówienia
        do_action('pco_before_order_form');
        ?>

        <?php
        // Renderowanie formularza na podstawie konfiguracji
        $form_renderer = new PCO_Form_Renderer();
        echo $form_renderer->render_form(true);
        ?>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Sprawdzenie czy pco_data jest dostępne
    if (typeof pco_data === 'undefined') {
        console.error('PCO: pco_data nie jest zdefiniowane');
        return;
    }

    // Sprawdzenie czy formularz istnieje
    var $form = $('#pco-order-form');
    if ($form.length === 0) {
        console.error('PCO: Formularz #pco-order-form nie został znaleziony');
        return;
    }

    console.log('PCO: Inicjalizacja obsługi formularza zamówienia');

    // Obsługa przesyłania formularza
    $form.on('submit', function(e) {
        e.preventDefault();
        console.log('PCO: Rozpoczęcie przesyłania formularza');

        var $submitButton = $form.find('.pco-submit-button');

        // Sprawdzenie czy przycisk submit istnieje
        if ($submitButton.length === 0) {
            console.error('PCO: Przycisk submit nie został znaleziony');
            alert('<?php _e('Błąd: Nie można znaleźć przycisku wysyłania formularza.', 'papierotka-custom-order'); ?>');
            return;
        }

        // Dezaktywacja przycisku
        $submitButton.prop('disabled', true).text('<?php _e('Przetwarzanie...', 'papierotka-custom-order'); ?>');

        // Pobranie danych formularza
        var formData = new FormData();
        var formDataObject = {};

        try {
            // Serializacja danych formularza do obiektu
            $form.serializeArray().forEach(function(item) {
                formDataObject[item.name] = item.value;
            });

            // Dodanie pliku z listą gości, jeśli został wybrany
            var guestListFileElement = $('#pco-guest-list-file')[0];
            if (guestListFileElement && guestListFileElement.files && guestListFileElement.files[0]) {
                formData.append('guest_list_file', guestListFileElement.files[0]);
            }

            // Sprawdzenie czy mamy wymagane dane
            if (!formDataObject.pco_product_id) {
                throw new Error('Brak ID produktu');
            }

            // Dodanie pozostałych danych
            formData.append('action', 'pco_submit_form');
            formData.append('nonce', pco_data.nonce);
            formData.append('form_data', JSON.stringify(formDataObject));

            // Dodanie debugowania
            console.log('PCO: Dane formularza:', formDataObject);
            console.log('PCO: ID produktu:', formDataObject.pco_product_id);
            console.log('PCO: Ilość:', formDataObject.pco_quantity);
            console.log('PCO: Opcje:', formDataObject.pco_options_data);
            console.log('PCO: Cena:', formDataObject.pco_total_price);
            console.log('PCO: Nonce:', pco_data.nonce);

        } catch (error) {
            console.error('PCO: Błąd podczas przygotowywania danych:', error);
            alert('<?php _e('Błąd podczas przygotowywania danych formularza: ', 'papierotka-custom-order'); ?>' + error.message);
            $submitButton.prop('disabled', false).text('<?php _e('Złóż zamówienie', 'papierotka-custom-order'); ?>');
            return;
        }

        // Wysłanie danych
        console.log('PCO: Wysyłanie formularza do:', pco_data.ajax_url);
        $.ajax({
            url: pco_data.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 30000, // 30 sekund timeout
            success: function(response) {
                console.log('PCO: Odpowiedź serwera:', response);

                try {
                    // Sprawdzenie czy odpowiedź jest prawidłowa
                    if (typeof response === 'string') {
                        response = JSON.parse(response);
                    }

                    if (response && response.success) {
                        console.log('PCO: Sukces - przekierowanie do:', response.data.redirect);
                        // Przekierowanie do strony potwierdzenia
                        window.location.href = response.data.redirect;
                    } else {
                        // Wyświetlenie błędu
                        var errorMessage = (response && response.data && response.data.message)
                            ? response.data.message
                            : '<?php _e('Wystąpił błąd podczas przetwarzania zamówienia. Spróbuj ponownie.', 'papierotka-custom-order'); ?>';
                        console.error('PCO: Błąd serwera:', errorMessage);
                        alert(errorMessage);
                        $submitButton.prop('disabled', false).text('<?php _e('Złóż zamówienie', 'papierotka-custom-order'); ?>');
                    }
                } catch (parseError) {
                    console.error('PCO: Błąd parsowania odpowiedzi:', parseError);
                    console.error('PCO: Surowa odpowiedź:', response);
                    alert('<?php _e('Błąd podczas przetwarzania odpowiedzi serwera.', 'papierotka-custom-order'); ?>');
                    $submitButton.prop('disabled', false).text('<?php _e('Złóż zamówienie', 'papierotka-custom-order'); ?>');
                }
            },
            error: function(xhr, status, error) {
                // Wyświetlenie błędu
                console.error('PCO: AJAX Error:', status, error);
                console.error('PCO: Response status:', xhr.status);
                console.error('PCO: Response text:', xhr.responseText);

                var errorMessage = '<?php _e('Wystąpił błąd podczas przetwarzania zamówienia.', 'papierotka-custom-order'); ?>';

                if (status === 'timeout') {
                    errorMessage = '<?php _e('Przekroczono limit czasu. Spróbuj ponownie.', 'papierotka-custom-order'); ?>';
                } else if (xhr.status === 403) {
                    errorMessage = '<?php _e('Błąd autoryzacji. Odśwież stronę i spróbuj ponownie.', 'papierotka-custom-order'); ?>';
                } else if (xhr.status === 500) {
                    errorMessage = '<?php _e('Błąd serwera. Skontaktuj się z administratorem.', 'papierotka-custom-order'); ?>';
                }

                alert(errorMessage);
                $submitButton.prop('disabled', false).text('<?php _e('Złóż zamówienie', 'papierotka-custom-order'); ?>');
            }
        });
    });
});
</script>

<?php
get_footer();