<?php
/**
 * Szablon strony formularza zamówienia
 */

// Zabezpieczenie przed bezpośrednim dostępem
if (!defined('ABSPATH')) {
    exit;
}

// Włączenie debugowania dla tego pliku
define('PCO_DEBUG', true);

// Sprawdzenie czy sesja WooCommerce jest aktywna
if (!WC()->session->has_session()) {
    pco_log('Brak aktywnej sesji WooCommerce na stronie formularza - próba inicjalizacji');
    WC()->session->set_customer_session_cookie(true);
}

// Sprawdzenie czy dane zamówienia są dostępne
$product_id = WC()->session->get('pco_product_id');
pco_log('Strona formularza - product_id z sesji: ' . ($product_id ? $product_id : 'brak'));

if (!$product_id) {
    pco_log('Brak danych produktu w sesji - przekierowanie na stronę główną');
    wp_redirect(home_url());
    exit;
}

get_header();
?>

<div class="pco-order-form-container">
    <div class="pco-order-form-wrapper">
        <h1><?php _e('Formularz zamówienia', 'papierotka-custom-order'); ?></h1>

        <?php
        // Wyświetlenie podsumowania zamówienia
        do_action('pco_before_order_form');
        ?>

        <?php
        // Renderowanie formularza na podstawie konfiguracji
        $form_renderer = new PCO_Form_Renderer();
        echo $form_renderer->render_form(true);
        ?>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Obsługa przesyłania formularza
    $('#pco-order-form').on('submit', function(e) {
        e.preventDefault();

        var $form = $(this);
        var $submitButton = $form.find('.pco-submit-button');

        // Dezaktywacja przycisku
        $submitButton.prop('disabled', true).text('<?php _e('Przetwarzanie...', 'papierotka-custom-order'); ?>');

        // Pobranie danych formularza
        var formData = new FormData();
        var formDataObject = {};

        // Serializacja danych formularza do obiektu
        $form.serializeArray().forEach(function(item) {
            formDataObject[item.name] = item.value;
        });

        // Dodanie pliku z listą gości, jeśli został wybrany
        var guestListFile = $('#pco-guest-list-file')[0].files[0];
        if (guestListFile) {
            formData.append('guest_list_file', guestListFile);
        }

        // Dodanie pozostałych danych
        formData.append('action', 'pco_submit_form');
        formData.append('nonce', pco_data.nonce);
        formData.append('form_data', JSON.stringify(formDataObject));

        // Dodanie debugowania
        if (window.console && window.console.log) {
            console.log('Dane formularza:', formDataObject);
            console.log('ID produktu:', formDataObject.pco_product_id);
            console.log('Ilość:', formDataObject.pco_quantity);
            console.log('Opcje:', formDataObject.pco_options_data);
            console.log('Cena:', formDataObject.pco_total_price);
        }

        // Wysłanie danych
        console.log('Wysyłanie formularza do:', pco_data.ajax_url);
        $.ajax({
            url: pco_data.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Odpowiedź serwera:', response);
                if (response.success) {
                    // Przekierowanie do strony potwierdzenia
                    window.location.href = response.data.redirect;
                } else {
                    // Wyświetlenie błędu
                    alert(response.data.message || '<?php _e('Wystąpił błąd podczas przetwarzania zamówienia. Spróbuj ponownie.', 'papierotka-custom-order'); ?>');
                    $submitButton.prop('disabled', false).text('<?php _e('Złóż zamówienie', 'papierotka-custom-order'); ?>');
                }
            },
            error: function(xhr, status, error) {
                // Wyświetlenie błędu
                console.error('AJAX Error:', status, error);
                console.error('Response:', xhr.responseText);
                alert('<?php _e('Wystąpił błąd podczas przetwarzania zamówienia. Spróbuj ponownie.', 'papierotka-custom-order'); ?>');
                $submitButton.prop('disabled', false).text('<?php _e('Złóż zamówienie', 'papierotka-custom-order'); ?>');
            }
        });
    });
});
</script>

<?php
get_footer();