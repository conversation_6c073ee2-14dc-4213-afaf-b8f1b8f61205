/**
 * Skrypt do bezpośredniej naprawy breadcrumbs na stronach zamówienia
 * Ten skrypt używa bardziej agresywnego podejścia, celując bezpośrednio w strukturę DOM
 */
document.addEventListener('DOMContentLoaded', function() {
    // Pobieranie informacji o stronie z URL
    var currentUrl = window.location.href;
    var urlParts = currentUrl.split('/');
    var lastUrlPart = '';
    
    // Znajdź ostatnią niepustą część URL (pomijając parametry)
    for (var i = urlParts.length - 1; i >= 0; i--) {
        var part = urlParts[i].split('?')[0]; // Usunięcie parametrów
        if (part && part.trim() !== '') {
            lastUrlPart = part;
            break;
        }
    }
    
    // Określenie, na której stronie jesteśmy
    var isOrderConfirmationPage = currentUrl.indexOf('potwierdzenie-zamowienia') > -1;
    var isOrderFormPage = currentUrl.indexOf('zamowienie') > -1 && !isOrderConfirmationPage;
    
    // Tytuł strony zależny od kontekstu
    var pageTitle = '';
    
    // Określenie tytułu strony na podstawie URL
    if (isOrderConfirmationPage) {
        pageTitle = 'Potwierdzenie zamówienia';
    } else if (isOrderFormPage) {
        pageTitle = 'Zamówienie'; // Zmiana na "Zamówienie" zgodnie z URL
    } else if (lastUrlPart) {
        // Dla innych stron użyj ostatniej części URL
        // Zamień myślniki na spacje i ustaw pierwszą literę jako wielką
        pageTitle = lastUrlPart.replace(/-/g, ' ').replace(/\b\w/g, function(l) { 
            return l.toUpperCase(); 
        });
    }
    
    // Jeśli mamy tytuł strony, napraw breadcrumbs
    if (pageTitle) {
        console.log('Wykryto stronę: ' + pageTitle + ' na podstawie URL: ' + currentUrl);
        
        // Natychmiastowa próba naprawy
        fixBreadcrumbsDirect(pageTitle);
        
        // Powtarzanie próby naprawy co 100ms przez 2 sekundy (dla elementów ładowanych dynamicznie)
        var attempts = 0;
        var interval = setInterval(function() {
            fixBreadcrumbsDirect(pageTitle);
            attempts++;
            if (attempts >= 20) {
                clearInterval(interval);
            }
        }, 100);
    }
    
    /**
     * Funkcja bezpośrednio naprawiająca breadcrumbs
     */
    function fixBreadcrumbsDirect(pageTitle) {
        console.log('Naprawianie breadcrumbs - zmiana "Blog" na "' + pageTitle + '"');
        
        // Dodanie CSS, który ukryje element "Blog" i zastąpi go naszym tytułem
        var style = document.createElement('style');
        style.textContent = `
            /* Ukrycie elementu "Blog" w breadcrumbs */
            .breadcrumb a:contains("Blog"), 
            .breadcrumbs a:contains("Blog"),
            .breadcrumb span:contains("Blog"),
            .breadcrumbs span:contains("Blog"),
            .breadcrumb li:contains("Blog"),
            .breadcrumbs li:contains("Blog") {
                font-size: 0 !important;
            }
            
            /* Dodanie naszego tytułu */
            .breadcrumb a:contains("Blog")::after, 
            .breadcrumbs a:contains("Blog")::after,
            .breadcrumb span:contains("Blog")::after,
            .breadcrumbs span:contains("Blog")::after,
            .breadcrumb li:contains("Blog")::after,
            .breadcrumbs li:contains("Blog")::after {
                content: "${pageTitle}";
                font-size: initial !important;
            }
        `;
        document.head.appendChild(style);
        
        // Bezpośrednia modyfikacja DOM dla elementu widocznego na zrzucie ekranu
        var breadcrumbElements = document.querySelectorAll('.breadcrumb, .breadcrumbs, .breadcrumb-trail, nav.breadcrumb');
        if (breadcrumbElements.length === 0) {
            // Jeśli nie znaleziono standardowych klas, szukamy po zawartości
            breadcrumbElements = Array.from(document.querySelectorAll('*')).filter(function(el) {
                return el.textContent.trim() === 'Home / Blog';
            });
        }
        
        // Modyfikacja znalezionych elementów
        breadcrumbElements.forEach(function(element) {
            // Sprawdzenie, czy element zawiera "Home / Blog"
            if (element.textContent.indexOf('Home / Blog') > -1) {
                // Utworzenie nowej zawartości
                var homeLink = document.createElement('a');
                homeLink.href = '/';
                homeLink.textContent = 'Home';
                
                var separator = document.createTextNode(' / ');
                
                var pageTitleSpan = document.createElement('span');
                pageTitleSpan.textContent = pageTitle;
                
                // Wyczyszczenie i dodanie nowej zawartości
                element.innerHTML = '';
                element.appendChild(homeLink);
                element.appendChild(separator);
                element.appendChild(pageTitleSpan);
            }
        });
        
        // Bezpośrednie wyszukiwanie węzłów tekstowych zawierających "Blog"
        var textNodes = [];
        function findTextNodes(node) {
            if (node.nodeType === 3) {
                if (node.nodeValue.trim() === 'Blog' || node.nodeValue.indexOf('Home / Blog') > -1) {
                    textNodes.push(node);
                }
            } else {
                for (var i = 0; i < node.childNodes.length; i++) {
                    findTextNodes(node.childNodes[i]);
                }
            }
        }
        
        // Przeszukanie całego dokumentu
        findTextNodes(document.body);
        
        // Modyfikacja znalezionych węzłów tekstowych
        textNodes.forEach(function(node) {
            if (node.nodeValue.trim() === 'Blog') {
                node.nodeValue = pageTitle;
            } else if (node.nodeValue.indexOf('Home / Blog') > -1) {
                node.nodeValue = node.nodeValue.replace('Home / Blog', 'Home / ' + pageTitle);
            }
        });
        
        // Dodatkowa poprawka dla nagłówków H1
        document.querySelectorAll('h1').forEach(function(h1) {
            // Jeśli nagłówek zawiera "Blog" lub "Potwierdzenie zamówienia" a nie powinien
            if (h1.textContent.trim() === 'Blog' || 
                (h1.textContent.trim() === 'Potwierdzenie zamówienia' && pageTitle !== 'Potwierdzenie zamówienia') ||
                (h1.textContent.trim() === 'Formularz zamówienia' && pageTitle !== 'Formularz zamówienia')) {
                h1.textContent = pageTitle;
            }
        });
        
        // Aktualizacja tytułu strony w przeglądarce
        if (document.title.indexOf('Blog') > -1) {
            document.title = document.title.replace('Blog', pageTitle);
        }
    }
    
    // Dodanie funkcji :contains do selektorów CSS
    (function(document) {
        var getElementsByContains = function(selector, text) {
            var elements = document.querySelectorAll(selector);
            return Array.prototype.filter.call(elements, function(element) {
                return RegExp(text).test(element.textContent);
            });
        };
        
        // Modyfikacja wszystkich elementów zawierających "Blog"
        var blogElements = getElementsByContains('*', 'Blog');
        blogElements.forEach(function(element) {
            if (element.textContent.trim() === 'Blog') {
                element.textContent = pageTitle;
            }
        });
    })(document);
});
