<?php
/**
 * Klasa zarządzająca zamówieniami
 */
class PCO_Order_Manager {

    /**
     * Konstruktor
     */
    public function __construct() {
        // Dodanie menu w panelu administracyjnym
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Dodanie skryptów i stylów dla panelu administracyjnego
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Obsługa AJAX
        add_action('wp_ajax_pco_get_orders', array($this, 'ajax_get_orders'));
        add_action('wp_ajax_pco_get_order_details', array($this, 'ajax_get_order_details'));
        add_action('wp_ajax_pco_update_order_status', array($this, 'ajax_update_order_status'));
        add_action('wp_ajax_pco_delete_order', array($this, 'ajax_delete_order'));
        add_action('wp_ajax_pco_export_orders', array($this, 'ajax_export_orders'));

        // Dodanie kolumny statusu do tabeli zamówień
        add_action('init', array($this, 'maybe_add_status_column'));
    }

    /**
     * Dodanie menu w panelu administracyjnym
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Zamówienia - Papierotka', 'papierotka-custom-order'),
            __('Zamówienia', 'papierotka-custom-order'),
            'manage_woocommerce',
            'pco-orders',
            array($this, 'admin_page')
        );
    }

    /**
     * Dodanie kolumny statusu do tabeli zamówień (jeśli nie istnieje)
     */
    public function maybe_add_status_column() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pco_orders';

        // Sprawdzenie czy kolumna status istnieje
        $column_exists = $wpdb->get_results($wpdb->prepare(
            "SHOW COLUMNS FROM {$table_name} LIKE %s",
            'status'
        ));

        if (empty($column_exists)) {
            // Dodanie kolumny status
            $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN status VARCHAR(20) DEFAULT 'new' AFTER created_at");

            // Dodanie kolumny notes
            $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN notes TEXT AFTER status");

            // Dodanie kolumny updated_at
            $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER notes");
        }
    }

    /**
     * Pobranie dostępnych statusów zamówień
     */
    public function get_order_statuses() {
        return array(
            'new' => array(
                'label' => __('Nowe', 'papierotka-custom-order'),
                'color' => '#0073aa',
                'icon' => 'dashicons-plus-alt'
            ),
            'in_progress' => array(
                'label' => __('W trakcie', 'papierotka-custom-order'),
                'color' => '#f56e28',
                'icon' => 'dashicons-clock'
            ),
            'completed' => array(
                'label' => __('Zakończone', 'papierotka-custom-order'),
                'color' => '#00a32a',
                'icon' => 'dashicons-yes-alt'
            ),
            'cancelled' => array(
                'label' => __('Anulowane', 'papierotka-custom-order'),
                'color' => '#d63638',
                'icon' => 'dashicons-dismiss'
            )
        );
    }

    /**
     * Pobranie zamówień z filtrowaniem
     */
    public function get_orders($args = array()) {
        global $wpdb;

        $defaults = array(
            'status' => '',
            'search' => '',
            'date_from' => '',
            'date_to' => '',
            'limit' => 20,
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC'
        );

        $args = wp_parse_args($args, $defaults);

        $table_name = $wpdb->prefix . 'pco_orders';
        $where_conditions = array('1=1');
        $where_values = array();

        // Filtrowanie po statusie
        if (!empty($args['status'])) {
            $where_conditions[] = 'status = %s';
            $where_values[] = $args['status'];
        }

        // Wyszukiwanie
        if (!empty($args['search'])) {
            $where_conditions[] = '(order_id LIKE %s OR form_data LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        // Filtrowanie po dacie
        if (!empty($args['date_from'])) {
            $where_conditions[] = 'created_at >= %s';
            $where_values[] = $args['date_from'] . ' 00:00:00';
        }

        if (!empty($args['date_to'])) {
            $where_conditions[] = 'created_at <= %s';
            $where_values[] = $args['date_to'] . ' 23:59:59';
        }

        $where_clause = implode(' AND ', $where_conditions);

        // Sanityzacja orderby i order
        $allowed_orderby = array('created_at', 'order_id', 'total_price', 'status', 'updated_at');
        $orderby = in_array($args['orderby'], $allowed_orderby) ? $args['orderby'] : 'created_at';
        $order = strtoupper($args['order']) === 'ASC' ? 'ASC' : 'DESC';

        // Zapytanie główne
        $query = "SELECT * FROM {$table_name} WHERE {$where_clause} ORDER BY {$orderby} {$order} LIMIT %d OFFSET %d";
        $where_values[] = intval($args['limit']);
        $where_values[] = intval($args['offset']);

        if (!empty($where_values)) {
            $prepared_query = $wpdb->prepare($query, $where_values);
        } else {
            $prepared_query = $query;
        }

        $orders = $wpdb->get_results($prepared_query);

        // Zapytanie dla liczby wszystkich rekordów
        $count_query = "SELECT COUNT(*) FROM {$table_name} WHERE {$where_clause}";
        if (!empty($where_values)) {
            // Usunięcie ostatnich dwóch wartości (limit i offset)
            $count_values = array_slice($where_values, 0, -2);
            if (!empty($count_values)) {
                $count_prepared = $wpdb->prepare($count_query, $count_values);
            } else {
                $count_prepared = $count_query;
            }
        } else {
            $count_prepared = $count_query;
        }

        $total_count = $wpdb->get_var($count_prepared);

        return array(
            'orders' => $orders,
            'total' => intval($total_count)
        );
    }

    /**
     * Pobranie szczegółów zamówienia
     */
    public function get_order($order_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pco_orders';

        $order = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_name} WHERE order_id = %s",
            $order_id
        ));

        if ($order) {
            // Dekodowanie danych formularza
            $order->form_data_decoded = json_decode($order->form_data, true);
            $order->options_data_decoded = json_decode($order->options_data, true);
        }

        return $order;
    }

    /**
     * Aktualizacja statusu zamówienia
     */
    public function update_order_status($order_id, $status, $notes = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pco_orders';
        $statuses = $this->get_order_statuses();

        if (!isset($statuses[$status])) {
            return false;
        }

        $result = $wpdb->update(
            $table_name,
            array(
                'status' => $status,
                'notes' => $notes,
                'updated_at' => current_time('mysql')
            ),
            array('order_id' => $order_id),
            array('%s', '%s', '%s'),
            array('%s')
        );

        return $result !== false;
    }

    /**
     * Usunięcie zamówienia
     */
    public function delete_order($order_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pco_orders';

        $result = $wpdb->delete(
            $table_name,
            array('order_id' => $order_id),
            array('%s')
        );

        return $result !== false;
    }

    /**
     * Pobranie statystyk zamówień
     */
    public function get_order_statistics() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pco_orders';
        $statuses = $this->get_order_statuses();

        $stats = array();

        // Statystyki według statusu
        foreach ($statuses as $status_key => $status_info) {
            $count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$table_name} WHERE status = %s",
                $status_key
            ));

            $stats['by_status'][$status_key] = array(
                'count' => intval($count),
                'label' => $status_info['label'],
                'color' => $status_info['color']
            );
        }

        // Całkowita liczba zamówień
        $stats['total'] = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");

        // Zamówienia z ostatnich 30 dni
        $stats['last_30_days'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE created_at >= %s",
            date('Y-m-d H:i:s', strtotime('-30 days'))
        ));

        // Całkowita wartość zamówień
        $stats['total_value'] = $wpdb->get_var("SELECT SUM(total_price) FROM {$table_name}");

        return $stats;
    }

    /**
     * Strona administracyjna zarządzania zamówieniami
     */
    public function admin_page() {
        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Nie masz uprawnień do tej strony.', 'papierotka-custom-order'));
        }

        $statuses = $this->get_order_statuses();
        $statistics = $this->get_order_statistics();

        ?>
        <div class="wrap pco-orders-admin">
            <h1><?php _e('Zarządzanie Zamówieniami', 'papierotka-custom-order'); ?></h1>

            <!-- Statystyki -->
            <div class="pco-statistics">
                <div class="pco-stat-card">
                    <h3><?php _e('Wszystkie zamówienia', 'papierotka-custom-order'); ?></h3>
                    <div class="pco-stat-number"><?php echo esc_html($statistics['total']); ?></div>
                </div>
                <div class="pco-stat-card">
                    <h3><?php _e('Ostatnie 30 dni', 'papierotka-custom-order'); ?></h3>
                    <div class="pco-stat-number"><?php echo esc_html($statistics['last_30_days']); ?></div>
                </div>
                <div class="pco-stat-card">
                    <h3><?php _e('Całkowita wartość', 'papierotka-custom-order'); ?></h3>
                    <div class="pco-stat-number"><?php echo number_format($statistics['total_value'], 2); ?> zł</div>
                </div>
            </div>

            <!-- Filtry -->
            <div class="pco-filters">
                <div class="pco-filter-row">
                    <input type="text" id="pco-search" placeholder="<?php _e('Szukaj zamówień...', 'papierotka-custom-order'); ?>" />

                    <select id="pco-status-filter">
                        <option value=""><?php _e('Wszystkie statusy', 'papierotka-custom-order'); ?></option>
                        <?php foreach ($statuses as $status_key => $status_info): ?>
                            <option value="<?php echo esc_attr($status_key); ?>"><?php echo esc_html($status_info['label']); ?></option>
                        <?php endforeach; ?>
                    </select>

                    <input type="date" id="pco-date-from" placeholder="<?php _e('Data od', 'papierotka-custom-order'); ?>" />
                    <input type="date" id="pco-date-to" placeholder="<?php _e('Data do', 'papierotka-custom-order'); ?>" />

                    <button type="button" class="button" id="pco-filter-btn"><?php _e('Filtruj', 'papierotka-custom-order'); ?></button>
                    <button type="button" class="button" id="pco-reset-filters"><?php _e('Resetuj', 'papierotka-custom-order'); ?></button>
                    <button type="button" class="button button-primary" id="pco-export-btn"><?php _e('Eksportuj CSV', 'papierotka-custom-order'); ?></button>
                </div>
            </div>

            <!-- Tabela zamówień -->
            <div class="pco-orders-table-container">
                <table class="wp-list-table widefat fixed striped" id="pco-orders-table">
                    <thead>
                        <tr>
                            <th><?php _e('ID Zamówienia', 'papierotka-custom-order'); ?></th>
                            <th><?php _e('Klient', 'papierotka-custom-order'); ?></th>
                            <th><?php _e('Produkt', 'papierotka-custom-order'); ?></th>
                            <th><?php _e('Wartość', 'papierotka-custom-order'); ?></th>
                            <th><?php _e('Status', 'papierotka-custom-order'); ?></th>
                            <th><?php _e('Data utworzenia', 'papierotka-custom-order'); ?></th>
                            <th><?php _e('Akcje', 'papierotka-custom-order'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="pco-orders-tbody">
                        <!-- Zamówienia będą ładowane przez AJAX -->
                    </tbody>
                </table>

                <div class="pco-loading" id="pco-orders-loading" style="display: none;">
                    <p><?php _e('Ładowanie zamówień...', 'papierotka-custom-order'); ?></p>
                </div>
            </div>

            <!-- Paginacja -->
            <div class="pco-pagination" id="pco-pagination">
                <!-- Paginacja będzie generowana przez JavaScript -->
            </div>
        </div>

        <!-- Modal szczegółów zamówienia -->
        <div id="pco-order-details-modal" class="pco-modal" style="display: none;">
            <div class="pco-modal-content">
                <span class="pco-modal-close">&times;</span>
                <h2><?php _e('Szczegóły zamówienia', 'papierotka-custom-order'); ?></h2>
                <div id="pco-order-details-content">
                    <!-- Szczegóły będą ładowane przez AJAX -->
                </div>
            </div>
        </div>

        <script type="text/javascript">
            window.pcoOrderStatuses = <?php echo json_encode($statuses); ?>;
        </script>
        <?php
    }

    /**
     * Dodanie skryptów i stylów dla panelu administracyjnego
     */
    public function enqueue_admin_scripts($hook) {
        // Ładowanie tylko na stronie zarządzania zamówieniami
        if ($hook !== 'woocommerce_page_pco-orders') {
            return;
        }

        wp_enqueue_style('pco-admin-orders', PCO_PLUGIN_URL . 'assets/css/admin-orders.css', array(), PCO_VERSION);
        wp_enqueue_script('pco-admin-orders', PCO_PLUGIN_URL . 'assets/js/admin-orders.js', array('jquery'), PCO_VERSION, true);

        // Przekazanie danych do skryptu
        wp_localize_script('pco-admin-orders', 'pco_orders_data', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('pco-admin-nonce'),
            'strings' => array(
                'loading' => __('Ładowanie...', 'papierotka-custom-order'),
                'error' => __('Wystąpił błąd', 'papierotka-custom-order'),
                'confirm_delete' => __('Czy na pewno chcesz usunąć to zamówienie?', 'papierotka-custom-order'),
                'no_orders' => __('Brak zamówień do wyświetlenia', 'papierotka-custom-order'),
                'export_success' => __('Eksport zakończony pomyślnie', 'papierotka-custom-order')
            )
        ));
    }

    /**
     * AJAX handler dla pobrania listy zamówień
     */
    public function ajax_get_orders() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pco-admin-nonce')) {
            wp_die('Błąd bezpieczeństwa');
        }

        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Brak uprawnień');
        }

        $args = array(
            'status' => sanitize_text_field($_POST['status'] ?? ''),
            'search' => sanitize_text_field($_POST['search'] ?? ''),
            'date_from' => sanitize_text_field($_POST['date_from'] ?? ''),
            'date_to' => sanitize_text_field($_POST['date_to'] ?? ''),
            'limit' => intval($_POST['limit'] ?? 20),
            'offset' => intval($_POST['offset'] ?? 0),
            'orderby' => sanitize_text_field($_POST['orderby'] ?? 'created_at'),
            'order' => sanitize_text_field($_POST['order'] ?? 'DESC')
        );

        $result = $this->get_orders($args);
        $statuses = $this->get_order_statuses();

        // Formatowanie danych zamówień
        $formatted_orders = array();
        foreach ($result['orders'] as $order) {
            $form_data = json_decode($order->form_data, true);
            $customer_name = $form_data['name'] ?? $form_data['customer_name'] ?? __('Nieznany', 'papierotka-custom-order');

            // Pobranie nazwy produktu
            $product = wc_get_product($order->product_id);
            $product_name = $product ? $product->get_name() : __('Produkt usunięty', 'papierotka-custom-order');

            $formatted_orders[] = array(
                'id' => $order->id,
                'order_id' => $order->order_id,
                'customer_name' => $customer_name,
                'product_name' => $product_name,
                'quantity' => $order->quantity,
                'total_price' => number_format($order->total_price, 2),
                'status' => $order->status ?? 'new',
                'status_label' => $statuses[$order->status ?? 'new']['label'] ?? __('Nieznany', 'papierotka-custom-order'),
                'status_color' => $statuses[$order->status ?? 'new']['color'] ?? '#666',
                'created_at' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($order->created_at)),
                'updated_at' => $order->updated_at ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($order->updated_at)) : ''
            );
        }

        wp_send_json_success(array(
            'orders' => $formatted_orders,
            'total' => $result['total'],
            'page' => floor($args['offset'] / $args['limit']) + 1,
            'total_pages' => ceil($result['total'] / $args['limit'])
        ));
    }

    /**
     * AJAX handler dla pobrania szczegółów zamówienia
     */
    public function ajax_get_order_details() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pco-admin-nonce')) {
            wp_die('Błąd bezpieczeństwa');
        }

        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Brak uprawnień');
        }

        $order_id = sanitize_text_field($_POST['order_id']);
        $order = $this->get_order($order_id);

        if (!$order) {
            wp_send_json_error(array('message' => __('Zamówienie nie zostało znalezione', 'papierotka-custom-order')));
            return;
        }

        // Pobranie nazwy produktu
        $product = wc_get_product($order->product_id);
        $product_name = $product ? $product->get_name() : __('Produkt usunięty', 'papierotka-custom-order');

        // Formatowanie danych formularza
        $form_data_formatted = array();
        if ($order->form_data_decoded) {
            foreach ($order->form_data_decoded as $key => $value) {
                if (!empty($value) && !in_array($key, array('pco_product_id', 'pco_quantity', 'pco_total_price', 'pco_options_data'))) {
                    $form_data_formatted[$key] = $value;
                }
            }
        }

        $statuses = $this->get_order_statuses();

        wp_send_json_success(array(
            'order' => array(
                'id' => $order->id,
                'order_id' => $order->order_id,
                'product_id' => $order->product_id,
                'product_name' => $product_name,
                'quantity' => $order->quantity,
                'total_price' => number_format($order->total_price, 2),
                'status' => $order->status ?? 'new',
                'status_label' => $statuses[$order->status ?? 'new']['label'] ?? __('Nieznany', 'papierotka-custom-order'),
                'notes' => $order->notes ?? '',
                'created_at' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($order->created_at)),
                'updated_at' => $order->updated_at ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($order->updated_at)) : '',
                'form_data' => $form_data_formatted,
                'options_data' => $order->options_data_decoded
            ),
            'statuses' => $statuses
        ));
    }

    /**
     * AJAX handler dla aktualizacji statusu zamówienia
     */
    public function ajax_update_order_status() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pco-admin-nonce')) {
            wp_die('Błąd bezpieczeństwa');
        }

        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Brak uprawnień');
        }

        $order_id = sanitize_text_field($_POST['order_id']);
        $status = sanitize_text_field($_POST['status']);
        $notes = sanitize_textarea_field($_POST['notes'] ?? '');

        if ($this->update_order_status($order_id, $status, $notes)) {
            wp_send_json_success(array(
                'message' => __('Status zamówienia został zaktualizowany', 'papierotka-custom-order')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Błąd podczas aktualizacji statusu', 'papierotka-custom-order')
            ));
        }
    }

    /**
     * AJAX handler dla usunięcia zamówienia
     */
    public function ajax_delete_order() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pco-admin-nonce')) {
            wp_die('Błąd bezpieczeństwa');
        }

        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Brak uprawnień');
        }

        $order_id = sanitize_text_field($_POST['order_id']);

        if ($this->delete_order($order_id)) {
            wp_send_json_success(array(
                'message' => __('Zamówienie zostało usunięte', 'papierotka-custom-order')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Błąd podczas usuwania zamówienia', 'papierotka-custom-order')
            ));
        }
    }

    /**
     * AJAX handler dla eksportu zamówień do CSV
     */
    public function ajax_export_orders() {
        // Sprawdzenie nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pco-admin-nonce')) {
            wp_die('Błąd bezpieczeństwa');
        }

        // Sprawdzenie uprawnień
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Brak uprawnień');
        }

        $args = array(
            'status' => sanitize_text_field($_POST['status'] ?? ''),
            'search' => sanitize_text_field($_POST['search'] ?? ''),
            'date_from' => sanitize_text_field($_POST['date_from'] ?? ''),
            'date_to' => sanitize_text_field($_POST['date_to'] ?? ''),
            'limit' => 1000, // Eksportujemy maksymalnie 1000 zamówień
            'offset' => 0,
            'orderby' => 'created_at',
            'order' => 'DESC'
        );

        $result = $this->get_orders($args);

        if (empty($result['orders'])) {
            wp_send_json_error(array(
                'message' => __('Brak zamówień do eksportu', 'papierotka-custom-order')
            ));
            return;
        }

        // Generowanie pliku CSV
        $filename = 'zamowienia_' . date('Y-m-d_H-i-s') . '.csv';
        $csv_content = $this->generate_csv_content($result['orders']);

        wp_send_json_success(array(
            'filename' => $filename,
            'content' => base64_encode($csv_content),
            'message' => __('Eksport zakończony pomyślnie', 'papierotka-custom-order')
        ));
    }

    /**
     * Generowanie zawartości pliku CSV
     */
    private function generate_csv_content($orders) {
        $statuses = $this->get_order_statuses();

        // Nagłówki CSV
        $headers = array(
            'ID Zamówienia',
            'Klient',
            'Email',
            'Telefon',
            'Produkt',
            'Ilość',
            'Cena',
            'Status',
            'Data utworzenia',
            'Data aktualizacji',
            'Notatki'
        );

        // Pobranie konfiguracji formularza dla dodatkowych pól
        $form_config = get_option('pco_form_config', array());
        $additional_fields = array();

        if (!empty($form_config['sections'])) {
            foreach ($form_config['sections'] as $section) {
                if (!empty($section['fields'])) {
                    foreach ($section['fields'] as $field) {
                        if (!empty($field['id']) && !in_array($field['id'], array('name', 'email', 'phone'))) {
                            $additional_fields[] = $field['id'];
                            $headers[] = $field['label'] ?? $field['id'];
                        }
                    }
                }
            }
        }

        // Rozpoczęcie generowania CSV
        $csv_content = '';

        // Dodanie BOM dla poprawnego wyświetlania polskich znaków w Excel
        $csv_content .= "\xEF\xBB\xBF";

        // Dodanie nagłówków
        $csv_content .= implode(';', array_map(array($this, 'csv_escape'), $headers)) . "\n";

        // Dodanie danych zamówień
        foreach ($orders as $order) {
            $form_data = json_decode($order->form_data, true);

            // Podstawowe dane
            $customer_name = $form_data['name'] ?? $form_data['customer_name'] ?? '';
            $customer_email = $form_data['email'] ?? $form_data['customer_email'] ?? '';
            $customer_phone = $form_data['phone'] ?? $form_data['customer_phone'] ?? '';

            // Pobranie nazwy produktu
            $product = wc_get_product($order->product_id);
            $product_name = $product ? $product->get_name() : 'Produkt usunięty';

            $row = array(
                $order->order_id,
                $customer_name,
                $customer_email,
                $customer_phone,
                $product_name,
                $order->quantity,
                number_format($order->total_price, 2),
                $statuses[$order->status ?? 'new']['label'] ?? 'Nieznany',
                date_i18n('Y-m-d H:i:s', strtotime($order->created_at)),
                $order->updated_at ? date_i18n('Y-m-d H:i:s', strtotime($order->updated_at)) : '',
                $order->notes ?? ''
            );

            // Dodanie dodatkowych pól
            foreach ($additional_fields as $field_id) {
                $row[] = $form_data[$field_id] ?? '';
            }

            $csv_content .= implode(';', array_map(array($this, 'csv_escape'), $row)) . "\n";
        }

        return $csv_content;
    }

    /**
     * Escapowanie wartości dla CSV
     */
    private function csv_escape($value) {
        // Konwersja na string
        $value = (string) $value;

        // Usunięcie tagów HTML
        $value = strip_tags($value);

        // Zamiana nowych linii na spacje
        $value = str_replace(array("\r\n", "\r", "\n"), ' ', $value);

        // Escapowanie cudzysłowów
        $value = str_replace('"', '""', $value);

        // Otoczenie cudzysłowami jeśli zawiera średnik, cudzysłów lub spację
        if (strpos($value, ';') !== false || strpos($value, '"') !== false || strpos($value, ' ') !== false) {
            $value = '"' . $value . '"';
        }

        return $value;
    }
}
