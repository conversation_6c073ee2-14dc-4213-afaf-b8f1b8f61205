/**
 * Error handler for external JavaScript issues
 * This script prevents external JavaScript errors from breaking the form submission
 */

jQuery(document).ready(function($) {
    // Global error handler to prevent external scripts from breaking our functionality
    window.addEventListener('error', function(e) {
        // Log the error but don't let it break our scripts
        if (window.console && window.console.error) {
            console.error('External script error caught:', e.message, 'at', e.filename + ':' + e.lineno);
        }
        
        // Don't prevent default - just log and continue
        return false;
    });

    // Handle specific known issues
    
    // Fix for progressBar null errors
    if (typeof progressBar === 'undefined') {
        window.progressBar = null;
    }
    
    // Fix for categoriesNavInner null errors
    if (typeof categoriesNavInner === 'undefined') {
        window.categoriesNavInner = null;
    }
    
    // Prevent jQuery errors from breaking our scripts
    var originalError = $.error;
    $.error = function(msg) {
        console.error('jQuery error caught:', msg);
        // Don't call the original error handler to prevent breaking
    };
    
    // Add null checks for common jQuery operations that might fail
    $.fn.safeFind = function(selector) {
        try {
            return this.find(selector);
        } catch (e) {
            console.error('Safe find error:', e);
            return $();
        }
    };
    
    // Override common problematic operations
    var originalFind = $.fn.find;
    $.fn.find = function(selector) {
        try {
            return originalFind.call(this, selector);
        } catch (e) {
            console.error('Find operation error:', e);
            return $();
        }
    };
    
    console.log('PCO: Error handler initialized');
});
