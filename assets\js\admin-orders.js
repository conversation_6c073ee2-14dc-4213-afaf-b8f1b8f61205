jQuery(document).ready(function($) {
    'use strict';

    // Globalne zmienne
    let currentPage = 1;
    let currentFilters = {};

    // Inicjalizacja
    init();

    function init() {
        bindEvents();
        loadOrders();
    }

    function bindEvents() {
        // Filtry
        $('#pco-filter-btn').on('click', function() {
            currentPage = 1;
            loadOrders();
        });

        $('#pco-reset-filters').on('click', function() {
            resetFilters();
        });

        // Wyszukiwanie na Enter
        $('#pco-search').on('keypress', function(e) {
            if (e.which === 13) {
                currentPage = 1;
                loadOrders();
            }
        });

        // Eksport
        $('#pco-export-btn').on('click', function() {
            exportOrders();
        });

        // Modal
        $(document).on('click', '.pco-btn-view', function() {
            const orderId = $(this).data('order-id');
            showOrderDetails(orderId);
        });

        $(document).on('click', '.pco-modal-close', function() {
            closeModal();
        });

        $(document).on('click', '.pco-modal', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Usuwanie zamówienia
        $(document).on('click', '.pco-btn-delete', function() {
            const orderId = $(this).data('order-id');
            deleteOrder(orderId);
        });

        // Aktualizacja statusu
        $(document).on('click', '#pco-update-status-btn', function() {
            updateOrderStatus();
        });

        // Paginacja
        $(document).on('click', '.pco-page-btn', function() {
            const page = parseInt($(this).data('page'));
            if (page !== currentPage) {
                currentPage = page;
                loadOrders();
            }
        });
    }

    function getFilters() {
        return {
            search: $('#pco-search').val(),
            status: $('#pco-status-filter').val(),
            date_from: $('#pco-date-from').val(),
            date_to: $('#pco-date-to').val()
        };
    }

    function resetFilters() {
        $('#pco-search').val('');
        $('#pco-status-filter').val('');
        $('#pco-date-from').val('');
        $('#pco-date-to').val('');
        currentPage = 1;
        loadOrders();
    }

    function loadOrders() {
        const $loading = $('#pco-orders-loading');
        const $tbody = $('#pco-orders-tbody');

        $loading.show();
        $tbody.empty();

        const filters = getFilters();
        currentFilters = filters;

        $.ajax({
            url: pco_orders_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pco_get_orders',
                nonce: pco_orders_data.nonce,
                ...filters,
                limit: 20,
                offset: (currentPage - 1) * 20
            },
            success: function(response) {
                $loading.hide();

                if (response.success) {
                    renderOrders(response.data.orders);
                    renderPagination(response.data.page, response.data.total_pages, response.data.total);
                } else {
                    showError(response.data.message || pco_orders_data.strings.error);
                }
            },
            error: function() {
                $loading.hide();
                showError(pco_orders_data.strings.error);
            }
        });
    }

    function renderOrders(orders) {
        const $tbody = $('#pco-orders-tbody');

        if (orders.length === 0) {
            $tbody.html('<tr><td colspan="7" style="text-align: center; padding: 40px;">' + pco_orders_data.strings.no_orders + '</td></tr>');
            return;
        }

        let html = '';
        orders.forEach(function(order) {
            html += `
                <tr>
                    <td><strong>${order.order_id}</strong></td>
                    <td>${order.customer_name}</td>
                    <td>${order.product_name}</td>
                    <td>${order.total_price} zł</td>
                    <td>
                        <span class="pco-status-badge pco-status-${order.status}" style="background-color: ${order.status_color}">
                            ${order.status_label}
                        </span>
                    </td>
                    <td>${order.created_at}</td>
                    <td>
                        <div class="pco-action-buttons">
                            <button type="button" class="pco-action-btn pco-btn-view" data-order-id="${order.order_id}">
                                Szczegóły
                            </button>
                            <button type="button" class="pco-action-btn pco-btn-delete" data-order-id="${order.order_id}">
                                Usuń
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        $tbody.html(html);
    }

    function renderPagination(currentPage, totalPages, totalItems) {
        const $pagination = $('#pco-pagination');

        if (totalPages <= 1) {
            $pagination.empty();
            return;
        }

        let html = `<span>Strona ${currentPage} z ${totalPages} (${totalItems} zamówień)</span>`;

        // Poprzednia strona
        if (currentPage > 1) {
            html += `<button type="button" class="pco-page-btn" data-page="${currentPage - 1}">« Poprzednia</button>`;
        }

        // Numery stron
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? ' current' : '';
            html += `<button type="button" class="pco-page-btn${activeClass}" data-page="${i}">${i}</button>`;
        }

        // Następna strona
        if (currentPage < totalPages) {
            html += `<button type="button" class="pco-page-btn" data-page="${currentPage + 1}">Następna »</button>`;
        }

        $pagination.html(html);
    }

    function showOrderDetails(orderId) {
        $.ajax({
            url: pco_orders_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pco_get_order_details',
                nonce: pco_orders_data.nonce,
                order_id: orderId
            },
            success: function(response) {
                if (response.success) {
                    renderOrderDetails(response.data.order, response.data.statuses);
                    $('#pco-order-details-modal').show();
                } else {
                    showError(response.data.message || pco_orders_data.strings.error);
                }
            },
            error: function() {
                showError(pco_orders_data.strings.error);
            }
        });
    }

    function renderOrderDetails(order, statuses) {
        let html = `
            <div class="pco-order-details">
                <div class="pco-order-info">
                    <div class="pco-info-section">
                        <h4>Informacje podstawowe</h4>
                        <div class="pco-info-item">
                            <span class="pco-info-label">ID:</span> ${order.order_id}
                        </div>
                        <div class="pco-info-item">
                            <span class="pco-info-label">Produkt:</span> ${order.product_name}
                        </div>
                        <div class="pco-info-item">
                            <span class="pco-info-label">Ilość:</span> ${order.quantity}
                        </div>
                        <div class="pco-info-item">
                            <span class="pco-info-label">Cena:</span> ${order.total_price} zł
                        </div>
                    </div>

                    <div class="pco-info-section">
                        <h4>Status i daty</h4>
                        <div class="pco-info-item">
                            <span class="pco-info-label">Status:</span>
                            <span class="pco-status-badge pco-status-${order.status}">${order.status_label}</span>
                        </div>
                        <div class="pco-info-item">
                            <span class="pco-info-label">Utworzono:</span> ${order.created_at}
                        </div>
                        ${order.updated_at ? `
                        <div class="pco-info-item">
                            <span class="pco-info-label">Zaktualizowano:</span> ${order.updated_at}
                        </div>
                        ` : ''}
                        ${order.notes ? `
                        <div class="pco-info-item">
                            <span class="pco-info-label">Notatki:</span><br>
                            ${order.notes}
                        </div>
                        ` : ''}
                    </div>
                </div>

                <div class="pco-form-data">
                    <h4>Dane z formularza</h4>
        `;

        // Dane formularza
        if (order.form_data && Object.keys(order.form_data).length > 0) {
            Object.keys(order.form_data).forEach(function(key) {
                if (order.form_data[key]) {
                    html += `
                        <div class="pco-form-field">
                            <div class="pco-field-label">${key}:</div>
                            <div class="pco-field-value">${order.form_data[key]}</div>
                        </div>
                    `;
                }
            });
        } else {
            html += '<p>Brak danych z formularza.</p>';
        }

        // Formularz aktualizacji statusu
        html += `
                </div>

                <div class="pco-status-update">
                    <h4>Aktualizuj status</h4>
                    <div class="pco-status-form">
                        <select id="pco-new-status">
        `;

        Object.keys(statuses).forEach(function(statusKey) {
            const selected = statusKey === order.status ? ' selected' : '';
            html += `<option value="${statusKey}"${selected}>${statuses[statusKey].label}</option>`;
        });

        html += `
                        </select>
                        <textarea id="pco-status-notes" placeholder="Notatki (opcjonalnie)">${order.notes || ''}</textarea>
                        <button type="button" class="button button-primary" id="pco-update-status-btn" data-order-id="${order.order_id}">
                            Aktualizuj status
                        </button>
                    </div>
                </div>
            </div>
        `;

        $('#pco-order-details-content').html(html);
    }

    function updateOrderStatus() {
        const orderId = $('#pco-update-status-btn').data('order-id');
        const status = $('#pco-new-status').val();
        const notes = $('#pco-status-notes').val();

        $.ajax({
            url: pco_orders_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pco_update_order_status',
                nonce: pco_orders_data.nonce,
                order_id: orderId,
                status: status,
                notes: notes
            },
            success: function(response) {
                if (response.success) {
                    closeModal();
                    loadOrders(); // Odświeżenie listy
                    showSuccess(response.data.message);
                } else {
                    showError(response.data.message || pco_orders_data.strings.error);
                }
            },
            error: function() {
                showError(pco_orders_data.strings.error);
            }
        });
    }

    function deleteOrder(orderId) {
        if (!confirm(pco_orders_data.strings.confirm_delete)) {
            return;
        }

        $.ajax({
            url: pco_orders_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pco_delete_order',
                nonce: pco_orders_data.nonce,
                order_id: orderId
            },
            success: function(response) {
                if (response.success) {
                    loadOrders(); // Odświeżenie listy
                    showSuccess(response.data.message);
                } else {
                    showError(response.data.message || pco_orders_data.strings.error);
                }
            },
            error: function() {
                showError(pco_orders_data.strings.error);
            }
        });
    }

    function exportOrders() {
        const filters = getFilters();

        $.ajax({
            url: pco_orders_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pco_export_orders',
                nonce: pco_orders_data.nonce,
                ...filters
            },
            success: function(response) {
                if (response.success) {
                    // Tworzenie i pobieranie pliku CSV
                    const csvContent = atob(response.data.content);
                    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                    const link = document.createElement('a');

                    if (link.download !== undefined) {
                        const url = URL.createObjectURL(blob);
                        link.setAttribute('href', url);
                        link.setAttribute('download', response.data.filename);
                        link.style.visibility = 'hidden';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    }

                    showSuccess(response.data.message);
                } else {
                    showError(response.data.message || pco_orders_data.strings.error);
                }
            },
            error: function() {
                showError(pco_orders_data.strings.error);
            }
        });
    }

    function closeModal() {
        $('#pco-order-details-modal').hide();
    }

    function showError(message) {
        // Można zastąpić bardziej zaawansowanym systemem powiadomień
        alert('Błąd: ' + message);
    }

    function showSuccess(message) {
        // Można zastąpić bardziej zaawansowanym systemem powiadomień
        alert('Sukces: ' + message);
    }
});