/* Styles for Order Management Admin Page */

.pco-orders-admin {
    max-width: 1400px;
}

/* Statistics Cards */
.pco-statistics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.pco-stat-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.pco-stat-card h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    font-weight: 500;
}

.pco-stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #0073aa;
    line-height: 1;
}

/* Filters */
.pco-filters {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.pco-filter-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.pco-filter-row input,
.pco-filter-row select {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.pco-filter-row input[type="text"] {
    min-width: 200px;
}

.pco-filter-row input[type="date"] {
    min-width: 140px;
}

.pco-filter-row select {
    min-width: 150px;
}

/* Orders Table */
.pco-orders-table-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    overflow-x: auto;
}

#pco-orders-table {
    margin: 0;
    border: none;
}

#pco-orders-table th {
    background: #f9f9f9;
    border-bottom: 1px solid #ddd;
    padding: 12px 8px;
    font-weight: 600;
    color: #333;
}

#pco-orders-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

#pco-orders-table tr:hover {
    background: #f9f9f9;
}

/* Status Badges */
.pco-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    color: #fff;
    white-space: nowrap;
}

.pco-status-new {
    background-color: #0073aa;
}

.pco-status-in_progress {
    background-color: #f56e28;
}

.pco-status-completed {
    background-color: #00a32a;
}

.pco-status-cancelled {
    background-color: #d63638;
}

/* Action Buttons */
.pco-action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.pco-action-btn {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1.2;
    border-radius: 3px;
    text-decoration: none;
    cursor: pointer;
    border: 1px solid transparent;
    background: none;
}

.pco-action-btn:hover {
    text-decoration: none;
}

.pco-btn-view {
    color: #0073aa;
    border-color: #0073aa;
}

.pco-btn-view:hover {
    background: #0073aa;
    color: #fff;
}

.pco-btn-delete {
    color: #d63638;
    border-color: #d63638;
}

.pco-btn-delete:hover {
    background: #d63638;
    color: #fff;
}

/* Loading State */
.pco-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.pco-loading::after {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: pco-spin 1s linear infinite;
}

@keyframes pco-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pagination */
.pco-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
    padding: 20px;
}

.pco-pagination button {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
    border-radius: 3px;
}

.pco-pagination button:hover:not(:disabled) {
    background: #f0f0f0;
}

.pco-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pco-pagination .current {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

/* Modal */
.pco-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.pco-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border: 1px solid #888;
    border-radius: 4px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.pco-modal-content h2 {
    margin: 0;
    padding: 20px;
    border-bottom: 1px solid #eee;
    background: #f9f9f9;
}

.pco-modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    right: 15px;
    top: 10px;
    cursor: pointer;
    z-index: 1;
}

.pco-modal-close:hover,
.pco-modal-close:focus {
    color: #000;
    text-decoration: none;
}

/* Order Details */
.pco-order-details {
    padding: 20px;
}

.pco-order-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.pco-info-section {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #eee;
}

.pco-info-section h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 600;
}

.pco-info-item {
    margin-bottom: 8px;
    font-size: 13px;
}

.pco-info-label {
    font-weight: 500;
    color: #666;
    display: inline-block;
    min-width: 80px;
}

.pco-form-data {
    margin-top: 20px;
}

.pco-form-data h4 {
    margin-bottom: 15px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.pco-form-field {
    margin-bottom: 10px;
    padding: 8px;
    background: #f9f9f9;
    border-radius: 3px;
    font-size: 13px;
}

.pco-field-label {
    font-weight: 500;
    color: #333;
    margin-bottom: 3px;
}

.pco-field-value {
    color: #666;
}

/* Status Update Form */
.pco-status-update {
    margin-top: 20px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #eee;
}

.pco-status-update h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.pco-status-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.pco-status-form select,
.pco-status-form textarea {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.pco-status-form textarea {
    min-height: 80px;
    resize: vertical;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pco-filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .pco-filter-row input,
    .pco-filter-row select {
        width: 100%;
        min-width: auto;
    }
    
    .pco-action-buttons {
        flex-direction: column;
    }
    
    .pco-modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }
    
    .pco-order-info {
        grid-template-columns: 1fr;
    }
}
